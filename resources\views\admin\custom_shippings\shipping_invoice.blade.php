@extends('layouts.app')

@push('css')
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endpush

@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])

        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card mb-6">
                    <h5 class="card-header">{{ $sub_title }}</h5>
                    <div class="card-body">
                        <form id="AddAirportForm" action="{{ route('admin.custom_shipping_invoices.store') }}"
                            method="POST" enctype="multipart/form-data">
                            @csrf
                            <input type="hidden" name="_method" id="_method" value="">
                            <input type="hidden" name="custom_shipping_id" value="{{ $id }}">
                            <div class="modal-body">
                                <div class="" id="modalInputsList">
                                    @if (isset($shippingInvoice) && count($shippingInvoice->shippingExpenses) > 0)
                                        @foreach ($shippingInvoice->shippingExpenses as $index => $item)
                                            <input type="hidden" name="shipping_expense[{{ $index }}][id]"
                                                value="{{ $item->id }}">
                                            <div class="row">
                                                <div class="col-md-6 mb-4">
                                                    <label for="shipping_expense_type_id"
                                                        class="form-label">{{ __('Expense Type') }}</label>
                                                    <select
                                                        name="shipping_expense[{{ $index }}][shipping_expense_type_id]"
                                                        class="select2">
                                                        @foreach ($shippingExpenseTypes as $type)
                                                            <option value="{{ $type->id }}"
                                                                {{ $item->shipping_expense_type_id == $type->id ? 'selected' : '' }}>
                                                                {{ $type->name_ar }} ( {{ $type->name_en }}
                                                                )
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('shipping_expense_type_id')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="col-md-5 mb-4">
                                                    <label for="amount" class="form-label">{{ __('Amount') }}</label>
                                                    <input type="number" id="amount"
                                                        name="shipping_expense[{{ $index }}][amount]"
                                                        class="form-control" value="{{ $item->amount }}" />
                                                    @error('amount')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                @if (!$loop->first)
                                                    <div class="col-md-1 d-flex align-self-center align-items-center">
                                                        <button type="button"
                                                            class="btn btn-sm btn-danger waves-effect waves-light text-nowrap mx-2 align-self-center deleteRowBtn"
                                                            data-id="{{ $item->id }}">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="row">
                                            <div class="col-md-6 mb-4">
                                                <label for="shipping_expense_type_id"
                                                    class="form-label">{{ __('Expense Type') }}</label>
                                                <select name="shipping_expense[0][shipping_expense_type_id]"
                                                    class="select2">
                                                    @foreach ($shippingExpenseTypes as $type)
                                                        <option value="{{ $type->id }}">{{ $type->name_ar }} (
                                                            {{ $type->name_en }}
                                                            )
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('shipping_expense_type_id')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                            <div class="col-md-5 mb-4">
                                                <label for="amount" class="form-label">{{ __('Amount') }}</label>
                                                <input type="number" id="amount" name="shipping_expense[0][amount]"
                                                    class="form-control" value="{{ old('amount') }}" />
                                                @error('amount')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    @endif

                                </div>
                                <br>
                                <hr>
                                @if (auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Change Shipping Invoice Status'))
                                    <div class="col-md-6 mb-4">
                                        <label for="status" class="form-label">{{ __('Invoice Status') }}</label>
                                        <select name="status" class="select2">
                                            @foreach ($shippingInvoiceStatus as $key => $value)
                                                <option value="{{ $value }}"
                                                    {{ isset($shippingInvoice) && $shippingInvoice->status == $value ? 'selected' : '' }}>
                                                    {{ $key }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('status')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                @endif
                                <div class="col-md-12 mb-4">
                                    <label for="formFile" class="form-label">{{ __('Attachments') }}</label>
                                    <input class="form-control" type="file" name="attachments[]" id="formFile"
                                        multiple />
                                    @error('attachment')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                                @if (isset($shippingInvoice) && count($shippingInvoice->attachments) > 0)
                                    <div class="col-xl-12 mx-auto">
                                        <table class="table mb-0">
                                            <thead class="table-light table-lighter">
                                                <tr>
                                                    <th>#</th>
                                                    <th>{{ __('Attachment') }}</th>
                                                    <th>{{ __('Actions') }}</th>
                                                </tr>
                                            </thead>

                                            <tbody id="tbodyList">
                                                @foreach ($shippingInvoice->attachments as $index => $attachment)
                                                    <tr>
                                                        <td>{{ $index + 1 }}</td>
                                                        <td>
                                                            <div class="">
                                                                <p>{{ $attachment->original_filename }}</p>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex">
                                                                <button type="button"
                                                                    class="btn btn-sm btn-danger deleteRow"
                                                                    data-id="{{ $attachment->id }}"><i
                                                                        class="fas fa-trash"></i></button>
                                                                <a href="{{ asset($attachment->file_path) }}"
                                                                    target="_blank" class="btn btn-sm btn-info mx-3"><i
                                                                        class="fas fa-eye"></i></a>
                                                                <a href="{{ asset($attachment->file_path) }}" download
                                                                    class="btn btn-sm btn-success"><i
                                                                        class="fas fa-download"></i></a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>

                                    </div>
                                @endif
                                <div class="col-md-12 mt-4 mb-4">
                                    <label for="notes" class="form-label">{{ __('Notes') }}</label>
                                    <textarea name="notes" class="form-control" id="">{{ @$shippingInvoice->notes }}</textarea>
                                    </select>
                                    @error('notes')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                                @if (isset($shippingInvoice->total_amount))
                                    <div class="row">
                                        <div class="col-md-5">
                                            <hr>
                                            <br>
                                            <div class="d-flex">
                                                <p class="">{{ __('Total: ') }}</p>
                                                <p class="mx-5">{{ $shippingInvoice->total_amount }}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="mt-6">
                                <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">
                                    {{ __('Close') }}
                                </button>
                                <button type="submit" class="btn btn-primary mx-4">{{ __('Submit') }}</button>

                                <button type="button" class="btn btn-success ml-auto"
                                    onclick="addNewRow()">{{ __('Add New Expense') }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('theme/assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script>
        function addNewRow() {
            const invoiceFields = document.getElementById('modalInputsList');
            const index = invoiceFields.children.length; // Get current index
            $("#modalInputsList").append(`
           <div class="row">
                        <div class="col-md-6 mb-4">
                            <label for="shipping_expense_type_id" class="form-label">{{ __('Expense Type') }}</label>
                            <select name="shipping_expense[${index}][shipping_expense_type_id]" class="select2">
                                @foreach ($shippingExpenseTypes as $type)
                                    <option value="{{ $type->id }}">{{ $type->name_ar }} ( {{ $type->name_en }} )
                                    </option>
                                @endforeach
                            </select>
                            @error('shipping_expense_type_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-5 mb-4">
                            <label for="amount" class="form-label">{{ __('Amount') }}</label>
                            <input type="number" id="amount" name="shipping_expense[${index}][amount]" class="form-control"
                                value="{{ old('amount') }}" />
                            @error('amount')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-1 d-flex align-self-center align-items-center">
                            <button type="button"
                                class="btn btn-sm btn-danger waves-effect waves-light text-nowrap mx-2 align-self-center deleteRowBtn">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
            `);
            $('.select2').select2({
                dropdownParent: $('#modalInputsList')
            });

        }

        $('.select2').select2({
            dropdownParent: $('#modalInputsList')
        });

        $("#modalInputsList").on("click", ".deleteRowBtn", function() {
            let id = $(this).attr("data-id");
            var el = $(this);
            if (id != "" && id != null && id != undefined) {
                deleteElement("{{ url('admin/shipping_expenses/') }}", id, el);
            } else {
                $(this).parent().parent().remove();
            }
        })

        $(".deleteRow").on("click", function() {
            let id = $(this).attr("data-id");
            var el = $(this);
            if (id != "" && id != null && id != undefined) {
                deleteElement("{{ url('admin/attachments/') }}", id, el);
            }
        })

        function deleteElement(route, id, el) {
            Swal.fire({
                title: "{{ __('Are you Sure?') }}",
                text: "{{ __('data will be deleted permenantly') }}",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "{{ __('Yes') }}",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    var token = $("#token").val();
                    $.post(route + "/" + id, {
                            _token: token,
                            _method: "DELETE"
                        },
                        function(data, status) {
                            if (status == "success") {
                                Swal.fire({
                                    icon: 'success',
                                    title: "{{ __('Deleted Successfully') }}",
                                    text: "{{ __('data has been deleted successfully') }}",
                                    customClass: {
                                        confirmButton: 'btn btn-success waves-effect waves-light'
                                    }
                                });
                                $(el).parent().parent().parent().remove();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: "{{ __('error') }}",
                                    text: "{{ __('something went wrong please try later') }}",
                                })
                            }

                        });
                }
            });

        }
    </script>
@endpush
