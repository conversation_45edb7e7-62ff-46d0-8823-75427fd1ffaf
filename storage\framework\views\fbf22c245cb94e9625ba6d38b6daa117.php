
        <?php echo $__env->make("layouts.export-partials.export_header", ['title' => __('Documents List')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <table class="custom-table">
        <thead>
            <tr>
                <th><?php echo e(__('Document Name')); ?></th>
                <th><?php echo e(__('Document Type')); ?></th>
                <th><?php echo e(__('End Date')); ?></th>
                <th><?php echo e(__('Status')); ?></th>
            </tr>
        </thead>
        <tbody>

            <?php $__currentLoopData = $documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($item->name); ?></td>
                    <td><?php echo e(@$item->documentType->name ?? '-'); ?></td>
                    <td><?php echo e(@$item->end_date ?? '-'); ?></td>
                    <td><?php echo e(__($item->remaining_days)); ?></td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
        <?php echo $__env->make("layouts.export-partials.export_footer", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php /**PATH E:\clearo\resources\views/admin/documents/export_pdf.blade.php ENDPATH**/ ?>