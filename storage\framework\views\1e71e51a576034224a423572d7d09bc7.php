<?php if($_Items != null && count($_Items) > 0): ?>
    <div class="card-datatable table-responsive">
        <table class="dt-advanced-search table">
            <thead class="table-light text-nowrap">
                <tr>
                    <th>#</th>
                    <?php if(is_admin()): ?>
                        <th><?php echo e(__('Name')); ?>

                            <span class="arrows-container">
                                <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="user_name"
                                    data-dir="asc"></i>
                                <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="user_name"
                                    data-dir="desc"></i>
                            </span>
                        </th>
                    <?php endif; ?>

                    <th><?php echo e(__('Company Name')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="user_name"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="user_name"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Amount')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="amount"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="amount"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Wallet Balance')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="wallet_balance"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="wallet_balance"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Actions')); ?>

                    </th>
                </tr>
            </thead>
            <tbody>
                <?php
                    $index = $startFrom;
                ?>
                <?php $__currentLoopData = $_Items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php echo e(++$index); ?>

                        </td>
                        <?php if(is_admin()): ?>
                            <td class="">
                                <?php echo e($item->user?->name); ?>

                            </td>
                        <?php endif; ?>
                        <td class="">
                            <?php echo e(@$item->user?->client?->name ?? '-'); ?>

                        </td>
                        <td>
                            <?php if($item->amount > 0): ?>
                                <?php echo e($item->amount); ?>

                            <?php else: ?>
                                <span class="text-danger"><?php echo e($item->amount); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if(@$item->user->wallet->amount > 0): ?>
                                <?php echo e($item->user->wallet->amount); ?>

                            <?php else: ?>
                                <span class="text-danger"><?php echo e(@$item->user->wallet->amount ?? 0); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="d-flex order-actions">
                                <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Update Client Debit')): ?>
                                    <a href="javascript:;"
                                        onclick="showUpdateBalanceModal('<?php echo e($item->id); ?>','<?php echo e($item->amount); ?>')"
                                        class="btn btn-sm btn-primary waves-effect waves-light text-nowrap">
                                        <?php echo e(__('Update Balance')); ?>

                                    </a>
                                <?php endif; ?>
                                <?php if(auth()->user()->IsSuperAdmin == 1 ||
                                        auth()->user()->can('Update Client Debit') ||
                                        auth()->user()->type == UserType::Client): ?>
                                    <a href="javascript:;"
                                        onclick="confirmFullPay('<?php echo e($item->id); ?>','<?php echo e($item->amount); ?>')"
                                        class="ms-3 btn btn-sm btn-danger waves-effect waves-light text-nowrap mx-2">
                                        <?php echo e(__('Full Pay')); ?>

                                    </a>
                                <?php endif; ?>
                                <?php if(auth()->user()->IsSuperAdmin == 1 ||
                                        auth()->user()->can('Update Client Debit') ||
                                        auth()->user()->type == UserType::Client): ?>
                                    <a href="javascript:;"
                                        onclick="showPartialPayModal('<?php echo e($item->id); ?>','<?php echo e($item->amount); ?>')"
                                        class="ms-3 btn btn-sm btn-secondary waves-effect waves-light text-nowrap mx-2">
                                        <?php echo e(__('Partial Pay')); ?>

                                    </a>
                                <?php endif; ?>

                                <?php if(auth()->user()->IsSuperAdmin == 1 && isset($item->user_id)): ?>
                                    <a href="<?php echo e(route('admin.custom_shipping_invoices.userInvoices', $item->user_id)); ?>"
                                        class="ms-3 btn btn-sm btn-success waves-effect waves-light text-nowrap">
                                        <?php echo e(__('View Invoices')); ?>

                                    </a>
                                <?php endif; ?>
                            </div>
                        </td>

                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
    <br>
    <?php echo $__env->make('layouts.partials.pagination', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
    <div class="p-6">
        <div class="alert alert-warning text-center">
            <?php echo e(__('Sorry, No Data Found')); ?>

        </div>
    </div>
<?php endif; ?>

<input type="hidden" id="PageNo" value="<?php echo e($currentPage); ?>" />
<input type="hidden" id="OrderBy" value="<?php echo e($orderBy); ?>" />
<input type="hidden" id="Direction" value="<?php echo e($direction); ?>" />

<script></script>
<?php /**PATH E:\clearo\resources\views/admin/client_debits/list.blade.php ENDPATH**/ ?>