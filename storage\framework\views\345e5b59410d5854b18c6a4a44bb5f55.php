<!-- Enhanced Dashboard Cards -->
<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.custom_shippings.create')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-plus-circle" ></i>
            </div>
            <h5 class="card-title mb-3 fw-bold text-dark" style="font-size: 1.1rem;">
                <?php echo e(__('Add New Shipping')); ?>

            </h5>
            <button class="btn btn-primary btn-sm px-4 py-2 fw-semibold" style="border-radius: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                <?php echo e(__('Create Now')); ?>

            </button>
        </div>
        <div class="position-absolute" style="top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(102, 126, 234, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard"  style="cursor: default;">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-search-location" style="font-size: 2rem;color: #fff;"></i>
            </div>
            <h5 class="card-title mb-3 fw-bold text-dark" style="font-size: 1.1rem;">
                <?php echo e(__('Track Custom Shipping')); ?>

            </h5>
            <button type="button" class="btn btn-primary btn-sm px-4 py-2 fw-semibold" style="border-radius: 25px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: none; box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);"
                onclick="showSearchModal()"><?php echo e(__('Search')); ?></button>
        </div>
        <div class="position-absolute" style="top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(240, 147, 251, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" style="cursor: default;">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-search-plus" style="font-size: 2rem;color: #fff;"></i>
            </div>
            <h5 class="card-title mb-3 fw-bold text-dark" style="font-size: 1.1rem;">
                <?php echo e(__('Search Hs Code')); ?>

            </h5>
            <button type="button" class="btn btn-primary btn-sm px-4 py-2 fw-semibold" style="border-radius: 25px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border: none; box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);"
                onclick="showHsCodeModal()"><?php echo e(__('Search')); ?></button>
        </div>
        <div class="position-absolute" style="top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(79, 172, 254, 0.1); border-radius: 50%;"></div>
    </div>
</div>
<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.custom_shippings.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-shipping-fast" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_custom_shippings); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Custom Shipping count')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(168, 237, 234, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard"
    data-route="<?php echo e(route('admin.custom_shippings.index', ['status' => CustomShippingStatus::Distributed])); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #38a169 0%, #68d391 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-check-circle" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_delivered_custom_shippings); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Distributed Custom Shippings')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(56, 161, 105, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard"
    data-route="<?php echo e(route('admin.custom_shippings.index', ['status' => CustomShippingStatus::UnderClearance])); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #d69e2e 0%, #f6e05e 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-clock" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_unfinished_custom_shippings); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Custom Shippings Under Clearance')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(214, 158, 46, 0.1); border-radius: 50%;"></div>
    </div>
</div>
<div class="col-lg-3 col-sm-6 dashcard d-none"
    data-route="<?php echo e(route('admin.custom_shippings.index', ['status' => CustomShippingStatus::ShipmentNotReceivedDelayed])); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #e53e3e 0%, #ff6b6b 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-exclamation-triangle" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_delayed_custom_shippings); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Delayed Custom Shippings')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(229, 62, 62, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.clients.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #2c7a7b 0%, #4fd1c7 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-users" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_clients); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Clients Management')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(44, 122, 123, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.users.index', ['type' => UserType::Broker])); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-user-tie" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_brokers); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Customs Brokers')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(139, 92, 246, 0.1); border-radius: 50%;"></div>
    </div>
</div>



<!--/ Card Border Shadow -->
<br>
<br>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.worker_dues.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #553c9a 0%, #8b5cf6 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-hand-holding-usd" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_workers_dues); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Total Brokers Due')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(85, 60, 154, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.client_debits.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-credit-card" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_clients_debits); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Total Clients Debit')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(253, 187, 45, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.advances.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%); border-radius: 50%; margin: 0 auto;">
                <!-- <i class="fas fa-arrow-up" ></i><i class="fas fa-money-bill-trend-up"></i> -->
                 <span class="money-arrow-icon">
                    <i class="fas fa-arrow-up"></i>
                    <i class="fas fa-dollar-sign"></i>
                </span>

            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_advances_to_workers); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Total Advances To Brokers')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(255, 154, 86, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.advances.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; margin: 0 auto;">
                
                <span class="money-arrow-icon">
                    <i class="fas fa-arrow-down" ></i>
                    <i class="fas fa-dollar-sign"></i>
                </span>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_advances_from_clients); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Total Advances From Clients')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(102, 126, 234, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.expenses.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-money-bill-wave" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_expenses); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Total Expenses')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(255, 107, 107, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.purchases.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-shopping-bag" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_purchases); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Total Purchases')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(116, 185, 255, 0.1); border-radius: 50%;"></div>
    </div>
</div>

<div class="col-lg-3 col-sm-6 dashcard" data-route="<?php echo e(route('admin.revenues.index')); ?>">
    <div class="card h-100 shadow-lg border-0 overflow-hidden position-relative" style="background: #ffffff; border: 1px solid #e3e6f0; transition: all 0.3s ease;">
        <div class="card-body text-center p-4">
            <div class="d-flex justify-content-center align-items-center mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); border-radius: 50%; margin: 0 auto;">
                <i class="fas fa-chart-line" ></i>
            </div>
            <h3 class="fw-bold mb-2" style="color: #2d3748; font-size: 2.5rem;"><?php echo e($total_revenues); ?></h3>
            <h6 class=" fw-semibold mb-0" style="font-size: 0.95rem;"><?php echo e(__('Total Revenues')); ?></h6>
        </div>
        <div class="position-absolute" style="bottom: -10px; left: -10px; width: 60px; height: 60px; background: rgba(0, 184, 148, 0.1); border-radius: 50%;"></div>
    </div>
</div>
<?php /**PATH E:\clearo\resources\views/admin/dashboard/components/admin.blade.php ENDPATH**/ ?>