<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>" />
    <link rel="stylesheet"
        href="<?php echo e(asset('theme/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css')); ?>" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="container-xxl flex-grow-1 container-p-y">
        <?php echo $__env->make('components.page-title', ['title' => $title], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="card">
            <div class="d-flex py-6 px-6">
                <div class="ml-4">
                    <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Create Revenue')): ?>
                        <a href="<?php echo e(route('admin.revenues.create')); ?>"
                            class="btn btn-primary me-2"><?php echo e(__('Add New Revenue')); ?></a>
                    <?php endif; ?>
                </div>
                <div class="btn-group mx-4">
                    <a href="javascript:;"
                        onclick="location.assign('<?php echo e(route('admin.revenues.exportAllToPdf')); ?>'  + '?category=' + $('#type_select').val())"
                        class="btn btn-success"><i class="fas fa-print"></i></a>

                </div>
            </div>
            <div class="row py-6 px-6">
                <div class="col-md-4">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;"><?php echo e(__('Search')); ?></label>
                        <div class="col-md-10">
                            <input class="form-control" type="search" id="Search" placeholder="<?php echo e(__('Search...')); ?>"
                                id="html5-search-input" />
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;"><?php echo e(__('Types')); ?></label>
                        <div class="col-md-10">
                            <select name="finance_category_id" id="type_select" class="form-control">
                                <option value="all" <?php echo e($current_type == 'all' ? 'selected' : ''); ?>><?php echo e(__('All')); ?>

                                </option>
                                <?php $__currentLoopData = $financeCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($cat->id); ?>" <?php if($current_type == $cat->id): echo 'selected'; endif; ?>><?php echo e($cat->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class=""><?php echo e(__('show')); ?></div>
                        <div class="mx-2">
                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class=""><?php echo e(__('entities')); ?></div>
                    </div>
                </div>
            </div>
            <div id="ListBodyDiv"></div>
        </div>

        <br><br>
        <div class="card">
            <div class="d-flex py-6 px-6">
                <div class="btn-group mx-4">
                    <a href="<?php echo e(route('admin.finance_categories.exportAllToPdf')); ?>" class="btn btn-success"
                        target="_blank"><i class="fas fa-print"></i></a>
                </div>
            </div>

            
            <div class="table-responsive card-datatable table-responsive">
                <table id="dataTable" class="card-datatable table-responsive table" cellspacing="0" width="100%">
                    <thead class="table-light table-lighter">
                        <tr>
                            <th><?php echo e(__('Type')); ?></th>
                            <th><?php echo e(__('Total Purchases')); ?></th>
                            <th><?php echo e(__('Total Revenues')); ?></th>
                            <th><?php echo e(__('Net Revenue')); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($item->name); ?></td>
                                <td><?php echo e($item->total_purchases); ?></td>
                                <td> <?php echo e($item->total_revenues); ?> </td>
                                <td><?php echo e($item->total_revenues - $item->total_purchases); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>

                </table>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')); ?>"></script>
    <script>
        $('.select2').select2({});

        var table_advances = $('#dataTable').DataTable({
            processing: true,
            serverSide: false,
            searching: true,
            paging: true,
            info: true,
            language: {
                emptyTable: "<?php echo e(__('No data available in table')); ?>"
            }
        });
        $(function() {
            $('#type_select').on('change', function() {
                var id = $(this).val();
                if (id) {
                    window.location = '<?php echo e(route('admin.purchases.index')); ?>' + '?category=' + id
                }
                return false;
            });
        });

        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(route('admin.revenues.list')); ?>",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function deleteUser(id) {
            Swal.fire({
                title: "<?php echo e(__('Are you Sure?')); ?>",
                text: "<?php echo e(__('data will be deleted permenantly')); ?>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "<?php echo e(__('Yes')); ?>",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    var token = $("#token").val();
                    $.post("<?php echo e(url('admin/revenues/')); ?>" + "/" + id, {
                            _token: token,
                            _method: "DELETE"
                        },
                        function(data, status) {
                            if (status == "success") {
                                Swal.fire({
                                    icon: 'success',
                                    title: "<?php echo e(__('Deleted Successfully')); ?>",
                                    text: "<?php echo e(__('data has been deleted successfully')); ?>",
                                    customClass: {
                                        confirmButton: 'btn btn-success waves-effect waves-light'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: "<?php echo e(__('error')); ?>",
                                    text: "<?php echo e(__('something went wrong please try later')); ?>",
                                })
                            }
                            var pageNo = $("#PageNo").val();
                            LoadData(pageNo);
                        });
                }
            });

        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/revenues/index.blade.php ENDPATH**/ ?>