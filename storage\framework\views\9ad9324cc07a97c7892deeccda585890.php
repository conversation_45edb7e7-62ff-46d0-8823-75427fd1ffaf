<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="container-xxl flex-grow-1 container-p-y">
        <?php echo $__env->make('components.page-title', ['title' => $title], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="card">
            <div class="d-flex py-6 px-6">
                <?php if($type == UserType::Client->value): ?>
                    <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Create Shipping Invoice')): ?>
                        <div class="ml-4">
                            <a href="<?php echo e(route('admin.custom_shipping_invoices.create')); ?>"
                                class="btn btn-primary"><?php echo e(__('Add Client Invoice')); ?></a>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

            </div>
            <div class="row py-6 px-6">
                <div class="col-md-4">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;"><?php echo e(__('Search')); ?></label>
                        <div class="col-md-10">
                            <input class="form-control" type="search" id="Search" placeholder="<?php echo e(__('Search...')); ?>"
                                id="html5-search-input" />
                        </div>
                    </div>
                </div>
                <div class="col-md-4"></div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class=""><?php echo e(__('show')); ?></div>
                        <div class="mx-2">
                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class=""><?php echo e(__('entities')); ?></div>
                    </div>
                </div>
            </div>
            <div id="ListBodyDiv"></div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script>
        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            var type = "<?php echo e($type); ?>"
            $.ajax({
                type: 'POST',
                url: `<?php echo e(route('admin.custom_shipping_invoices.list')); ?>`,
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    UserType: type,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function deleteRecord(id) {
            Swal.fire({
                title: "<?php echo e(__('Are you Sure?')); ?>",
                text: "<?php echo e(__('data will be deleted permenantly')); ?>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "<?php echo e(__('Yes')); ?>",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    var token = $("#token").val();
                    $.post("<?php echo e(url('admin/custom_shipping_invoices/')); ?>" + "/" + id, {
                            _token: token,
                            _method: "DELETE"
                        },
                        function(data, status) {
                            if (status == "success") {
                                Swal.fire({
                                    icon: 'success',
                                    title: "<?php echo e(__('Deleted Successfully')); ?>",
                                    text: "<?php echo e(__('data has been deleted successfully')); ?>",
                                    customClass: {
                                        confirmButton: 'btn btn-success waves-effect waves-light'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: "<?php echo e(__('error')); ?>",
                                    text: "<?php echo e(__('something went wrong please try later')); ?>",
                                })
                            }
                            var pageNo = $("#PageNo").val();
                            LoadData(pageNo);
                        });
                }
            });

        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/shipping_invoices/invoices.blade.php ENDPATH**/ ?>