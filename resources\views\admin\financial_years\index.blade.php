@extends('layouts.app')

@push('css')
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <style>
        .ul-list {
            padding: .3rem 1.3rem;
        }
    </style>
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])

        @include('admin.financial_years.partials.filter')
        <div class="row g-4 my-6">
            @include('admin.financial_years.months')
        </div>
        {{-- <div class="row g-4 my-6" id="ListBodyDiv">

        </div> --}}
    </div>
@endsection

@push('js')
    <script src="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script>
        function LoadData(pageNo) {
            var search = $("#Search").val();
            var rowsPerPage = 15;
            var token = $("#token").val();
            var financial_year_id = $("#financial_year_id").val();
            var start_from = $("#start_from").val();
            var end_to = $("#end_to").val();
            console.log(start_from);
            console.log(end_to);
            console.log(financial_year_id);
            $.ajax({
                type: 'POST',
                url: "{{ route('admin.financial_months.list') }}",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    financial_year_id,
                    start_from,
                    end_to,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
    </script>
@endpush
