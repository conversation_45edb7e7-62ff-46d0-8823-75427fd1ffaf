@extends('layouts.app')

@push('css')
    <style>
        .showMore {
            cursor: pointer;
        }
    </style>
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        <div class="card d-none" id="cardTable">
            <div class="row py-6 px-6">
                <div class="col-md-4">
                    <button class="btn btn-success btn-sm" id="showSearchCard">{{ __('New Search') }}</button>
                </div>
                <div class="col-md-4"></div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class="">{{ __('show') }}</div>
                        <div class="mx-2">
                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="">{{ __('entities') }}</div>
                    </div>
                </div>
            </div>
            <div id="ListBodyDiv"></div>
        </div>

        <div class="row" id="cardSearch">
            <div class="col-md-4 mx-auto">
                <div class="card">
                    <div class="card-body">
                        <h4 class="text-center">{{ __('Search') }}</h4>
                        <div class="alert alert-danger my-3 error-info d-none"></div>
                        <div class="">
                            <label for="html5-search-input" class="col-form-label"
                                style="text-wrap:nowrap;">{{ __('Search By Code') }}</label>
                            <input class="form-control" type="search" id="Code" placeholder="{{ __('Code...') }}"
                                id="html5-search-input" />
                        </div>
                        <div class="">
                            <label for="html5-search-input" class="col-form-label"
                                style="text-wrap:nowrap;">{{ __('Search By Name') }}</label>
                            <input class="form-control" type="search" id="Name" placeholder="{{ __('Name...') }}"
                                id="html5-search-input" />
                        </div>
                        <div class="pt-4 text-center">
                            <button class="btn btn-success btn-sm mx-auto" id="searchBtn">{{ __('Search') }}

                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        var Code = "{{ $code }}";
        var Name = "{{ $description }}";

        if ((Code != null && Code != "") || (Name != "" && Name != null)) {
            $("#Code").val(Code);
            $("#Name").val(Name);
            searchSheet();
        }

        $("#searchBtn").on("click", function() {
            searchSheet();
        });
        $("#showSearchCard").on("click", function() {
            $("#searchBtn").text("{{ __('Search') }}");
            $("#searchBtn").removeAttr("disabled");
            $("#cardTable").addClass("d-none");
            $("#cardSearch").removeClass("d-none");

        })

        function searchSheet() {
            Code = $("#Code").val();
            Name = $("#Name").val();
            if ((Code == null && Name == null) || (Code == "" && Name == "")) {
                $(".error-info").removeClass("d-none")
                $(".error-info").text("{{ __('No Code or Name Added') }}")
                return;
            } else {
                $(".error-info").addClass("d-none")
            }

            $("#searchBtn").text("{{ __('Searching...') }}");
            $("#searchBtn").attr("disabled", "");
            LoadData(1);
        }

        function LoadData(pageNo) {
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "{{ route('admin.hs_sheet.list') }}",
                data: {
                    PageNo: pageNo,
                    Code: Code,
                    Name: Name,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");

                    $("#cardTable").removeClass("d-none");
                    $("#cardSearch").addClass("d-none");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            //LoadData(1);
        });

        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                //LoadData(pageNo);
                searchSheet()
            }
        });

        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
    </script>
@endpush
