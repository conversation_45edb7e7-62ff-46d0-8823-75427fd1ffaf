@extends('layouts.app')

@push('css')
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        <div class="row g-6">
            <!-- Navigation -->
            @include('admin.intialization.components.navigation_menu')
            <!-- /Navigation -->

            <!-- Options -->
            <div class="col-12 col-lg-8 pt-6 pt-lg-0">
                <div class="tab-content p-0">
                    <!-- Store Details Tab -->
                    <div class="tab-pane fade show active" id="store_details" role="tabpanel">
                        <div class="card">
                            <div class="d-flex py-6 px-6">
                                <div class="ml-4">
                                    <button type="button" onclick="showCreateModal()"
                                        class="btn btn-primary">{{ __('Add New') }}</button>
                                </div>
                            </div>
                            <div class="row py-6 px-6">
                                <div class="col-md-5">
                                    <div class="row">
                                        <label for="html5-search-input" class="col-md-2 col-form-label"
                                            style="text-wrap:nowrap;">{{ __('Search') }}</label>
                                        <div class="col-md-10">
                                            <input class="form-control" type="search" id="Search"
                                                placeholder="{{ __('Search...') }}" id="html5-search-input" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3"></div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center justify-content-end">
                                        <div class="">{{ __('show') }}</div>
                                        <div class="mx-2">
                                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                                <option value="10">10</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                        </div>
                                        <div class="">{{ __('entities') }}</div>
                                    </div>
                                </div>
                            </div>
                            <div id="ListBodyDiv"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /Options-->
        </div>
    </div>

    <div class="modal fade" id="addForm" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <form id="AddHarborForm" action="{{ route('admin.expense_types.store') }}" method="POST">
                    @csrf
                    <input type="hidden" name="_method" id="_method" value="">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel3">{{ __('Add New Expense') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="name_ar" class="form-label">{{ __('Arabic Name') }}</label>
                                <input type="text" id="name_ar" name="name_ar" class="form-control"
                                    value="{{ old('name_ar') }}" />
                                @error('name_ar')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-4">
                                <label for="name_en" class="form-label">{{ __('English Name') }}</label>
                                <input type="text" id="name_en" name="name_en" class="form-control"
                                    value="{{ old('name_en') }}" />
                                @error('name_en')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-4">
                                <label for="status" class="form-label">{{ __('Status') }}</label>
                                <select name="status" name="status" class="form-control" id="status">
                                    <option value="1">{{ __('Active') }}</option>
                                    <option value="0">{{ __('Inactive') }}</option>
                                </select>
                                @error('status')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">
                            {{ __('Close') }}
                        </button>
                        <button type="submit" class="btn btn-primary">{{ __('Submit') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/@form-validation/popular.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/@form-validation/bootstrap5.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/@form-validation/auto-focus.js') }}"></script>
    <script>
        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "{{ route('admin.expense_types.list') }}",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });

        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function deleteUser(id) {
            Swal.fire({
                title: "{{ __('Are you Sure?') }}",
                text: "{{ __('data will be deleted permenantly') }}",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "{{ __('Yes') }}",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    var token = $("#token").val();
                    $.post("{{ url('admin/expense_types/') }}" + "/" + id, {
                            _token: token,
                            _method: "DELETE"
                        },
                        function(data, status) {
                            if (status == "success") {
                                Swal.fire({
                                    icon: 'success',
                                    title: "{{ __('Deleted Successfully') }}",
                                    text: "{{ __('data has been deleted successfully') }}",
                                    customClass: {
                                        confirmButton: 'btn btn-success waves-effect waves-light'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: "{{ __('error') }}",
                                    text: "{{ __('something went wrong please try later') }}",
                                })
                            }
                            var pageNo = $("#PageNo").val();
                            LoadData(pageNo);
                        });
                }
            });

        }

        const formAuthentication = document.querySelector('#AddHarborForm');

        function showCreateModal() {
            $("#AddHarborForm").attr("action", "{{ route('admin.expense_types.store') }}");
            $("#_method").val("POST");
            $("#AddHarborForm")[0].reset();
            $(".modal-title").text("{{ __('Add New Expense') }}")
            $("#addForm").modal("show");
        }

        function showUpdateModal(id) {
            $.ajax({
                type: 'GET',
                url: "{{ url('admin/expense_types') }}" + `/${id}`,
                cache: false,
                dataType: "json",
                success: function(res) {
                    console.log(res);
                    if (res.success == true) {
                        res.data["is_default"] = res.data["is_default"] ? "1" : "0"
                        for (const key in res.data) {
                            if (res.data.hasOwnProperty(key)) {
                                const input = document.querySelector(`[name="${key}"]`);
                                if (input) {
                                    input.value = res.data[key];
                                }
                            }
                        }
                        $("#AddHarborForm").attr("action", "{{ url('admin/expense_types') }}" + `/${id}`);
                        $("#_method").val("PUT");
                        $(".modal-title").text("{{ __('Update Expense') }}")
                        $("#addForm").modal("show");
                    } else {
                        alertError(res.error)
                    }
                },
                error: function(err) {
                    console.log(err)
                }
            });

        }
    </script>
@endpush
