<?php $__env->startSection('content'); ?>
    <div class="container-xxl flex-grow-1 container-p-y">
        <?php echo $__env->make('components.page-title', ['title' => $title], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="row g-4">
            <?php if(is_admin()): ?>
                <?php echo $__env->make('admin.dashboard.components.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php elseif(is_broker()): ?>
                <?php echo $__env->make('admin.dashboard.components.broker', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php elseif(is_system_employee()): ?>
                <?php echo $__env->make('admin.dashboard.components.system_employee', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php elseif(is_client() || is_sub_client()): ?>
                <?php echo $__env->make('admin.dashboard.components.client', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        </div>
    </div>

    <?php echo $__env->make('admin.dashboard.components.search_shipping', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('admin.dashboard.components.search_hs_code', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('admin.dashboard.components.print_account_statement', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        function showSearchModal() {
            $(".error-info").addClass("d-none");
            $("#basicCrudModal").modal("show");
        }

        function showHsCodeModal() {
            $(".error-info").addClass("d-none");
            $("#searchHsCodeModal").modal("show");
        }

        function showAccountStatementModal() {
            $(".error-info").addClass("d-none");
            $("#printAccountStatementMondal").modal("show");
        }

        // Enhanced card interactions
        $(document).ready(function() {
            // Add click functionality to dashboard cards
            $('.dashcard').on('click', function() {
                const route = $(this).data('route');
                if (route) {
                    window.location.href = route;
                }
            });

            // Add hover effects
            $('.dashcard').hover(
                function() {
                    $(this).find('.card').addClass('shadow-lg');
                    $(this).css('transform', 'translateY(-5px)');
                },
                function() {
                    $(this).find('.card').removeClass('shadow-lg');
                    $(this).css('transform', 'translateY(0)');
                }
            );

            // Add loading animation for cards with data-route
            $('.dashcard[data-route]').on('click', function() {
                const card = $(this);
                const originalContent = card.html();

                // Show loading state
                card.find('.card-body').html(`
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 mb-0"><?php echo e(__('Loading...')); ?></p>
                    </div>
                `);

                // Restore original content after a short delay if navigation fails
                setTimeout(() => {
                    card.html(originalContent);
                }, 3000);
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/dashboard/index.blade.php ENDPATH**/ ?>