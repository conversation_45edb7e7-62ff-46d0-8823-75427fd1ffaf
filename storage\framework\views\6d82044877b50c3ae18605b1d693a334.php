<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="container-xxl flex-grow-1 container-p-y">
        <?php echo $__env->make('components.page-title', ['title' => $title], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="card">
            <div class="row py-6 px-6">
                <div class="col-md-5">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;"><?php echo e(__('Search')); ?></label>
                        <div class="col-md-10">
                            <input class="form-control" type="search" id="Search" placeholder="<?php echo e(__('Search...')); ?>"
                                id="html5-search-input" />
                        </div>
                    </div>
                </div>
                <div class="col-md-3"></div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class=""><?php echo e(__('show')); ?></div>
                        <div class="mx-2">
                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class=""><?php echo e(__('entities')); ?></div>
                    </div>
                </div>
            </div>
            <div id="ListBodyDiv"></div>
        </div>
    </div>

    <div class="modal fade" id="detailsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel1"><?php echo e(__('Details')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="content-text py-4"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">
                        <?php echo e(__('Close')); ?>

                    </button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script>
        $('.select2').select2({});

        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(route('admin.authentication_logs.list')); ?>",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on('click', '.btn-show-text', function() {
            var text = $(this).data('text');
            $(".content-text").text(text);
            $("#detailsModal").modal("show");
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/logs/authentication_logs/index.blade.php ENDPATH**/ ?>