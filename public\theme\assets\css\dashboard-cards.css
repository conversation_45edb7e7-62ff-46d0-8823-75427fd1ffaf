/* Dashboard Cards Styling */
body{
    font-family: '<PERSON><PERSON>', sans-serif !important;
}
.dashcard {
    cursor: pointer;
    transition: all 0.3s ease;
}

.dashcard:hover {
    transform: translateY(-5px);
}

.dashcard .card {
    border: 1px solid #e3e6f0;
    border-radius: 15px;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.dashcard:hover .card {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d9e6;
}

.dashcard .card-body {
    padding: 1.5rem;
}

.dashcard .d-flex {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dashcard .d-flex i {
    font-size: 2rem;
    color: #fff;
}

.dashcard h3 {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2d3748;
    font-size: 2.5rem;
}

.dashcard h5 {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.dashcard h6 {
    font-size: 0.95rem;
    color: #6c757d;
    font-weight: 500;
}

.dashcard .btn {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: none;
    color: #fff;
}

.dashcard .position-absolute {
    position: absolute;
    bottom: -10px;
    left: -10px;
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
}

/* Card color variations */
/* .card-gradient-purple {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-gradient-blue {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-gradient-pink {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-gradient-green {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
}

.card-gradient-orange {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);
}

.card-gradient-yellow {
    background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.card-gradient-red {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
}

.card-gradient-teal {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.card-gradient-indigo {
    background: linear-gradient(135deg, #c1dfc4 0%, #deecdd 100%);
}

.card-gradient-lavender {
    background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
}

.card-gradient-peach {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.card-gradient-rose {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
} */

/* Animation effects */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.dashcard:hover .icon-circle {
    animation: pulse 1.5s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashcard .card-value {
        font-size: 2rem;
    }
    
    .dashcard .icon-circle {
        width: 60px;
        height: 60px;
    }
    
    .dashcard .icon-circle i {
        font-size: 1.5rem;
    }
}

@media (max-width: 520px) {
    .custom-btn-media {
        display: block !important;
    }

}
