/* Sidebar Icons Styling to Match Dashboard Cards */

.menu-vertical .menu-item .menu-link {
    display: flex;
    gap: 10px;
}

.menu-icon-wrapper {
    display: flex;
    align-items: center;
}

.menu-icon-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.menu-icon-circle i {
    font-size: 1.2rem;
    color: #ffffff;
    transition: all 0.3s ease;
}

.menu-item.active .menu-icon-circle {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .menu-icon-circle {
        width: 30px;
        height: 30px;
    }
    
    .menu-icon-circle i {
        font-size: 1rem;
    }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .menu-icon-circle {
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
    }
    
    .menu-item:hover .menu-icon-circle {
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
    }
}

/* Smooth transitions for all menu elements */
.menu-link {
    transition: all 0.3s ease;
}

.menu-item .menu-link:hover {
    background-color: rgba(115, 103, 240, 0.08);
    border-radius: 8px;
}

/* Active state styling */
.menu-item.active .menu-link {
    background-color: rgba(115, 103, 240, 0.12);
    border-radius: 8px;
}

/* Sub-menu icon adjustments */
.menu-sub .menu-item .menu-icon-circle {
    width: 25px;
    height: 25px;
}

.menu-sub .menu-item .menu-icon-circle i {
    font-size: 0.9rem;
}

/* Modern Page Title with Icon Styling */
.page-title-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 0;
    padding: 1rem 0;
}

.page-title-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.page-title-icon i {
    font-size: 1.5rem;
    color: #ffffff;
    transition: all 0.3s ease;
}

.page-title-text {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    line-height: 1.2;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Hover effects for page title */
.page-title-container:hover .page-title-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments for page titles */
@media (max-width: 768px) {
    .page-title-icon {
        width: 40px;
        height: 40px;
    }

    .page-title-icon i {
        font-size: 1.2rem;
    }

    .page-title-text {
        font-size: 1.5rem;
    }

    .page-title-container {
        gap: 10px;
        padding: 0.5rem 0;
    }
}

/* Dark mode adjustments for page titles */
@media (prefers-color-scheme: dark) {
    .page-title-text {
        text-shadow: 0 1px 3px rgba(255, 255, 255, 0.1);
    }

    .page-title-icon {
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
    }

    .page-title-container:hover .page-title-icon {
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
    }
}

/* Modern Navigation Menu Styling */
.modern-nav-menu {
    gap: 8px;
}

.modern-nav-link {
    display: flex !important;
    align-items: center;
    gap: 12px;
    padding: 12px 16px !important;
    border-radius: 12px !important;
    transition: all 0.3s ease;
    background: transparent !important;
    border: none !important;
    color: #6c757d !important;
    font-weight: 500;
}

.modern-nav-link:hover {
    background: rgba(115, 103, 240, 0.08) !important;
    color: #7367f0 !important;
    transform: translateX(4px);
}

.modern-nav-link.active {
    background: rgba(115, 103, 240, 0.12) !important;
    color: #7367f0 !important;
    font-weight: 600;
}

.modern-nav-link .menu-icon-wrapper {
    display: flex;
    align-items: center;
}

.modern-nav-link .menu-icon-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modern-nav-link .menu-icon-circle i {
    font-size: 1rem;
    color: #ffffff;
    transition: all 0.3s ease;
}

.modern-nav-link:hover .menu-icon-circle {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.modern-nav-link.active .menu-icon-circle {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments for modern nav menu */
@media (max-width: 768px) {
    .modern-nav-link .menu-icon-circle {
        width: 28px;
        height: 28px;
    }

    .modern-nav-link .menu-icon-circle i {
        font-size: 0.9rem;
    }

    .modern-nav-link {
        gap: 10px;
        padding: 10px 14px !important;
    }
}

/* Additional styling for navigation cards */
.modern-nav-menu .nav-item:last-child .modern-nav-link {
    margin-bottom: 0;
}

/* Smooth animation for navigation items */
.modern-nav-link span {
    transition: all 0.3s ease;
}

.modern-nav-link:hover span {
    color: #7367f0 !important;
}

.modern-nav-link.active span {
    color: #7367f0 !important;
}

/* Enhanced card styling for navigation */
.modern-nav-menu .nav-item .modern-nav-link {
    margin-bottom: 4px;
    border: 1px solid transparent;
}

.modern-nav-menu .nav-item .modern-nav-link:hover {
    border-color: rgba(115, 103, 240, 0.2);
    box-shadow: 0 2px 8px rgba(115, 103, 240, 0.1);
}

.modern-nav-menu .nav-item .modern-nav-link.active {
    border-color: rgba(115, 103, 240, 0.3);
    box-shadow: 0 4px 12px rgba(115, 103, 240, 0.15);
}
