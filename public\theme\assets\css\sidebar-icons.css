/* Sidebar Icons Styling to Match Dashboard Cards */

.menu-vertical .menu-item .menu-link {
    display: flex;
    gap: 10px;
}

.menu-icon-wrapper {
    display: flex;
    align-items: center;
}

.menu-icon-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.menu-icon-circle i {
    font-size: 1.2rem;
    color: #ffffff;
    transition: all 0.3s ease;
}

.menu-item.active .menu-icon-circle {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .menu-icon-circle {
        width: 30px;
        height: 30px;
    }
    
    .menu-icon-circle i {
        font-size: 1rem;
    }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .menu-icon-circle {
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
    }
    
    .menu-item:hover .menu-icon-circle {
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
    }
}

/* Smooth transitions for all menu elements */
.menu-link {
    transition: all 0.3s ease;
}

.menu-item .menu-link:hover {
    background-color: rgba(115, 103, 240, 0.08);
    border-radius: 8px;
}

/* Active state styling */
.menu-item.active .menu-link {
    background-color: rgba(115, 103, 240, 0.12);
    border-radius: 8px;
}

/* Sub-menu icon adjustments */
.menu-sub .menu-item .menu-icon-circle {
    width: 25px;
    height: 25px;
}

.menu-sub .menu-item .menu-icon-circle i {
    font-size: 0.9rem;
}

/* Modern Page Title with Icon Styling */
.page-title-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 0;
    padding: 1rem 0;
}

.page-title-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.page-title-icon i {
    font-size: 1.5rem;
    color: #ffffff;
    transition: all 0.3s ease;
}

.page-title-text {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    line-height: 1.2;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Hover effects for page title */
.page-title-container:hover .page-title-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments for page titles */
@media (max-width: 768px) {
    .page-title-icon {
        width: 40px;
        height: 40px;
    }

    .page-title-icon i {
        font-size: 1.2rem;
    }

    .page-title-text {
        font-size: 1.5rem;
    }

    .page-title-container {
        gap: 10px;
        padding: 0.5rem 0;
    }
}

/* Dark mode adjustments for page titles */
@media (prefers-color-scheme: dark) {
    .page-title-text {
        text-shadow: 0 1px 3px rgba(255, 255, 255, 0.1);
    }

    .page-title-icon {
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
    }

    .page-title-container:hover .page-title-icon {
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
    }
}
