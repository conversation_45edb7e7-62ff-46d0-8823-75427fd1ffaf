<?php
    use App\Helpers\PageIconsHelper;
    
    // Get current route name
    $currentRoute = Route::currentRouteName();
    
    // Get icon configuration for current route
    $iconConfig = PageIconsHelper::getPageIcon($currentRoute);
    
    // Default icon if no specific icon found
    $defaultIcon = [
        'icon' => 'fas fa-file-alt',
        'background' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    ];
    
    $icon = $iconConfig['icon'] ?? $defaultIcon['icon'];
    $background = $iconConfig['background'] ?? $defaultIcon['background'];
?>

<div class="page-title-container">
    <div class="page-title-icon" style="background: <?php echo e($background); ?>;">
        <i class="<?php echo e($icon); ?>"></i>
    </div>
    <h4 class="page-title-text"><?php echo e($title); ?></h4>
</div>
<?php /**PATH E:\clearo\resources\views/components/page-title.blade.php ENDPATH**/ ?>