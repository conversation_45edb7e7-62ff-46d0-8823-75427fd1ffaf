@extends('layouts.app')

@push('css')
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
    <link rel="stylesheet"
        href="{{ asset('theme/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css') }}" />
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        <div class="card mb-3">
            <div class="d-flex py-6 px-6">
                <div class="ml-4">
                    @if (auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Create Purchase'))
                        <a href="{{ route('admin.purchases.create') }}"
                            class="btn btn-primary me-2">{{ __('Add New Purchase') }}</a>
                    @endif
                </div>
                <div class="btn-group mx-4">
                    <a href="javascript:;"
                        onclick="location.assign('{{ route('admin.purchases.exportAllToPdf') }}'  + '?category=' + $('#type_select').val())"
                        class="btn btn-success"><i class="fas fa-print"></i></a>

                </div>
            </div>
            <div class="row py-6 px-6">
                <div class="col-md-4">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;">{{ __('Search') }}</label>
                        <div class="col-md-10">
                            <input class="form-control" type="search" id="Search" placeholder="{{ __('Search...') }}"
                                id="html5-search-input" />
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;">{{ __('Types') }}</label>
                        <div class="col-md-10">
                            <select name="finance_category_id" id="type_select" class="form-control">
                                <option value="all" {{ $current_type == 'all' ? 'selected' : '' }}>{{ __('All') }}
                                </option>
                                @foreach ($financeCategories as $cat)
                                    <option value="{{ $cat->id }}" @selected($current_type == $cat->id)>{{ $cat->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class="">{{ __('show') }}</div>
                        <div class="mx-2">
                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="">{{ __('entities') }}</div>
                    </div>
                </div>
            </div>
            <div id="ListBodyDiv"></div>
        </div>
        <br><br>
        <div class="card">
            <div class="d-flex py-6 px-6">
                <div class="btn-group mx-4">
                    <a href="{{ route('admin.finance_categories.exportAllToPdf') }}" class="btn btn-success"
                        target="_blank"><i class="fas fa-print"></i></a>
                </div>
            </div>

            {{-- Table --}}
            <div class="table-responsive card-datatable table-responsive">
                <table id="dataTable" class="card-datatable table-responsive table" cellspacing="0" width="100%">
                    <thead class="table-light table-lighter">
                        <tr>
                            <th>{{ __('Type') }}</th>
                            <th>{{ __('Total Purchases') }}</th>
                            <th>{{ __('Total Revenues') }}</th>
                            <th>{{ __('Net Revenue') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($categories as $item)
                            <tr>
                                <td>{{ $item->name }}</td>
                                <td>{{ $item->total_purchases }}</td>
                                <td> {{ $item->total_revenues }} </td>
                                <td>{{ $item->total_revenues - $item->total_purchases }}</td>
                            </tr>
                        @endforeach
                    </tbody>

                </table>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script>
        $('.select2').select2({});

        var table_advances = $('#dataTable').DataTable({
            processing: true,
            serverSide: false,
            searching: true,
            paging: true,
            info: true,
            language: {
                emptyTable: "{{ __('No data available in table') }}"
            }
        });
        $(function() {
            $('#type_select').on('change', function() {
                var id = $(this).val();
                if (id) {
                    window.location = '{{ route('admin.purchases.index') }}' + '?category=' + id
                }
                return false;
            });
        });

        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "{{ route('admin.purchases.list') }}",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    SelectedType: "{{ $current_type }}",
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function deleteUser(id) {
            Swal.fire({
                title: "{{ __('Are you Sure?') }}",
                text: "{{ __('data will be deleted permenantly') }}",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "{{ __('Yes') }}",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    var token = $("#token").val();
                    $.post("{{ url('admin/purchases/') }}" + "/" + id, {
                            _token: token,
                            _method: "DELETE"
                        },
                        function(data, status) {
                            if (status == "success") {
                                Swal.fire({
                                    icon: 'success',
                                    title: "{{ __('Deleted Successfully') }}",
                                    text: "{{ __('data has been deleted successfully') }}",
                                    customClass: {
                                        confirmButton: 'btn btn-success waves-effect waves-light'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: "{{ __('error') }}",
                                    text: "{{ __('something went wrong please try later') }}",
                                })
                            }
                            var pageNo = $("#PageNo").val();
                            LoadData(pageNo);
                        });
                }
            });

        }
    </script>
@endpush
