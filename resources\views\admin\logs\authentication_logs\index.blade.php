@extends('layouts.app')

@push('css')
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        <div class="card">
            <div class="row py-6 px-6">
                <div class="col-md-5">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;">{{ __('Search') }}</label>
                        <div class="col-md-10">
                            <input class="form-control" type="search" id="Search" placeholder="{{ __('Search...') }}"
                                id="html5-search-input" />
                        </div>
                    </div>
                </div>
                <div class="col-md-3"></div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class="">{{ __('show') }}</div>
                        <div class="mx-2">
                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="">{{ __('entities') }}</div>
                    </div>
                </div>
            </div>
            <div id="ListBodyDiv"></div>
        </div>
    </div>

    <div class="modal fade" id="detailsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel1">{{ __('Details') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="content-text py-4"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">
                        {{ __('Close') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/select2/select2.js') }}"></script>
    <script>
        $('.select2').select2({});

        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "{{ route('admin.authentication_logs.list') }}",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on('click', '.btn-show-text', function() {
            var text = $(this).data('text');
            $(".content-text").text(text);
            $("#detailsModal").modal("show");
        });
    </script>
@endpush
