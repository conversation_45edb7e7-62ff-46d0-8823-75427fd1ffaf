<head>
    <meta charset="utf-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

    <title><?php echo e($title); ?></title>

    <meta name="description" content="" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon"
        href="<?php echo e(asset(@get_setting('favicon') && get_setting('favicon') != '' ? get_setting('favicon') : 'theme/assets/img/favicon.ico')); ?>" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/fonts/tabler-icons.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/fonts/fontawesome.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/fonts/flag-icons.css')); ?>" />

    <!-- Core CSS -->

    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/css/rtl/core.css')); ?>"
        class="template-customizer-core-css" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/css/rtl/theme-default.css')); ?>"
        class="template-customizer-theme-css" />

    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/css/demo.css')); ?>" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/notifications/css/lobibox.min.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/node-waves/node-waves.css')); ?>" />

    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css')); ?>" />

    <!-- Page CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/css/dashboard-cards.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/css/sidebar-icons.css')); ?>" />

    <!-- Helpers -->
    <script src="<?php echo e(asset('theme/assets/vendor/js/helpers.js')); ?>"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->

    <!--? Template customizer: To hide customizer set displayCustomizer value false in config.js.  -->
    <script src="<?php echo e(asset('theme/assets/vendor/js/template-customizer.js')); ?>"></script>

    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script src="<?php echo e(asset('theme/assets/js/config.js')); ?>"></script>

    <style>
        body {
            font-size: 1rem;
            font-family: 'Almarai', sans-serif !important;
        }

        .error {
            color: #dc3545;
        }

        .arrows-container {
            position: relative;
            margin: 0 10px;

        }

        .fa-caret-up {
            position: absolute;
            top: -4px;
            bottom: 1px;
            cursor: pointer;
            color: #aca9a9;
        }

        .fa-caret-down {
            position: absolute;
            bottom: 0;
            top: 7px;
            cursor: pointer;
            color: #aca9a9;
            transition: .2 ease-in-out;
        }

        .fa-caret-down:hover {
            color: #066b7c;
        }

        .fa-caret-up:hover {
            color: #066b7c;
        }

        .arrow-selected {
            color: #066b7c;
        }

        .bg-orange {
            background-color: #f29028;
        }

        table th {
            font-weight: bold;
        }
    </style>
    <?php echo $__env->yieldPushContent('css'); ?>




    <input type="hidden" id="token" value="<?php echo e(csrf_token()); ?>">
</head>
<?php /**PATH E:\clearo\resources\views/layouts/partials/styles.blade.php ENDPATH**/ ?>