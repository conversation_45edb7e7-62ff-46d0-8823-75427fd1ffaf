<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/css/custom-arabic-styles.css')); ?>" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="container-xxl flex-grow-1 container-p-y">
        <h4 class="py-4 mb-6"><?php echo e($title); ?></h4>
        <div class="card mb-6">
            <h5 class="card-header"><?php echo e($sub_title); ?></h5>
            <form class="card-body" action="<?php echo e(route('admin.documents.store')); ?>" method="POST" enctype="multipart/form-data"
                id="CreateForm">
                <?php echo csrf_field(); ?>
                <div class="row g-6">
                    <div class="col-md-6">
                        <label class="form-label" for="document_type_id"><?php echo e(__('Company Name')); ?></label>
                        <select name="document_type_id" id="document_type_id" class="select2">
                            <?php $__currentLoopData = $document_types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($type->id); ?>"><?php echo e($type->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['document_type_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6">
                        <label for="name_ar" class="form-label"><?php echo e(__('Arabic Name')); ?></label>
                        <input type="text" id="name_ar" name="name_ar" class="form-control"
                            value="<?php echo e(old('name_ar')); ?>" />
                        <?php $__errorArgs = ['name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 ">
                        <label for="name_en" class="form-label"><?php echo e(__('English Name')); ?></label>
                        <input type="text" id="name_en" name="name_en" class="form-control"
                            value="<?php echo e(old('name_en')); ?>" />
                        <?php $__errorArgs = ['name_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label" for="end_date"><?php echo e(__('End Date')); ?></label>
                        <input type="date" id="end_date" name="end_date" class="form-control"
                            value="<?php echo e(old('end_date')); ?>" />
                        <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="notify_before"><?php echo e(__('Notify Before')); ?></label>
                        <input type="number" id="notify_before" name="notify_before" class="form-control"
                            value="<?php echo e(old('notify_before')); ?>" />
                        <?php $__errorArgs = ['notify_before'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="col-md-6">
                        <label for="formFile" class="form-label"><?php echo e(__('Attachments')); ?></label>
                        <input class="form-control" type="file" name="attachments[]" id="formFile" multiple />
                        <?php $__errorArgs = ['attachment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-12">
                        <label class="form-label" for="description"><?php echo e(__('Description')); ?></label>
                        <textarea name="description" id="description" class="form-control"></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="pt-6">
                    <button type="submit" class="btn btn-primary me-4"><?php echo e(__('Submit')); ?></button>
                    <a href="<?php echo e(route('admin.documents.index')); ?>" class="btn btn-label-secondary"><?php echo e(__('Cancel')); ?></a>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script>
        $('.select2').val('').select2({
            allowClear: true,
            placeholder: "<?php echo e(__('Select')); ?>"
        });
        $('#CreateForm').validate({
            errorElement: 'span',
            rules: {
                name_ar: {
                    required: true,
                },
                name_en: {
                    required: true,
                },
                document_type_id: {
                    required: true,
                },
                "attachments[]": {
                    required: true,
                },
            },
            messages: {
                name_ar: {
                    required: "<?php echo e(CrudMessage::isRequired(__('Arabic Name'))); ?>",
                },
                name_en: {
                    required: "<?php echo e(CrudMessage::isRequired(__('English Name'))); ?>",
                },
                document_type_id: {
                    required: "<?php echo e(CrudMessage::isRequired(__('Document Type'))); ?>",
                },
                "attachments[]": {
                    required: "<?php echo e(CrudMessage::isRequired(__('Attachments'))); ?>",
                },
            },
            errorPlacement: function(error, element) {
                // Check if the element is a Select2 element
                if (element.hasClass('select2-hidden-accessible')) {
                    error.insertAfter(element.next(
                        '.select2-container')); // Place error after Select2 container
                } else {
                    error.insertAfter(element); // Default behavior for other inputs
                }
            },
            submitHandler: function(form) {
                $(".submitBtn").attr("disabled", "true")
                form.submit();
            }
        });
    </script>
    <script src="<?php echo e(asset('theme/assets/js/custom-arabic-translations.js')); ?>"></script>
    <script>
        window.appLocale = '<?php echo e(app()->getLocale()); ?>';
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/documents/create.blade.php ENDPATH**/ ?>