<?php

namespace App\Helpers;

class PageIconsHelper
{
    /**
     * Get the icon configuration for a specific route
     * 
     * @param string $route
     * @return array|null
     */
    public static function getPageIcon($route)
    {
        $icons = self::getIconsMap();
        
        // Check for exact route match first
        if (isset($icons[$route])) {
            return $icons[$route];
        }
        
        // Check for route prefix matches
        foreach ($icons as $routePattern => $iconConfig) {
            if (str_starts_with($route, $routePattern)) {
                return $iconConfig;
            }
        }
        
        return null;
    }
    
    /**
     * Get all icons mapping
     * 
     * @return array
     */
    private static function getIconsMap()
    {
        return [
            // Dashboard
            'admin.dashboard' => [
                'icon' => 'fas fa-tachometer-alt',
                'background' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
            ],
            
            // Custom Shippings
            'admin.custom_shippings' => [
                'icon' => 'fas fa-cubes',
                'background' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
            ],
            
            // Shipping Invoices
            'admin.custom_shipping_invoices' => [
                'icon' => 'fas fa-file-invoice',
                'background' => 'linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%)'
            ],
            
            // Financial Months & Monthly Accounting
            'admin.financial_months' => [
                'icon' => 'far fa-calendar-alt',
                'background' => 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)'
            ],
            'admin.withdraw' => [
                'icon' => 'far fa-calendar-alt',
                'background' => 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)'
            ],
            
            // Revenues & Expenses
            'admin.expenses' => [
                'icon' => 'fas fa-money-bill-wave',
                'background' => 'linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)'
            ],
            'admin.purchases' => [
                'icon' => 'fas fa-shopping-cart',
                'background' => 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)'
            ],
            'admin.revenues' => [
                'icon' => 'fas fa-chart-line',
                'background' => 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)'
            ],
            
            // Payment Overview / Advances
            'admin.advances' => [
                'icon' => 'fas fa-hand-holding-usd',
                'background' => 'linear-gradient(135deg, #553c9a 0%, #8b5cf6 100%)'
            ],
            
            // Client Debits & Worker Dues
            'admin.client_debits' => [
                'icon' => 'fas fa-wallet',
                'background' => 'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)'
            ],
            'admin.worker_dues' => [
                'icon' => 'fas fa-wallet',
                'background' => 'linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)'
            ],
            
            // Users Management
            'admin.users' => [
                'icon' => 'fas fa-users-cog',
                'background' => 'linear-gradient(135deg, #f36bcd 0%, #f5e57c 100%)'
            ],
            
            // Clients
            'admin.clients' => [
                'icon' => 'fas fa-users',
                'background' => 'linear-gradient(135deg, #2c7a7b 0%, #4fd1c7 100%)'
            ],
            
            // Companies
            'admin.companies' => [
                'icon' => 'fas fa-building',
                'background' => 'linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%)'
            ],
            
            // Documents
            'admin.documents' => [
                'icon' => 'far fa-file-alt',
                'background' => 'linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%)'
            ],
            
            // HS Sheet / Tariff Lookup
            'admin.hs_sheet' => [
                'icon' => 'fas fa-file-alt',
                'background' => 'linear-gradient(135deg, #f3c382 0%, #ef7e59 100%)'
            ],
            
            // Roles
            'admin.roles' => [
                'icon' => 'fas fa-user-shield',
                'background' => 'linear-gradient(135deg, #f36bcd 0%, #f5e57c 100%)'
            ],
            
            // Logs
            'admin.authentication_logs' => [
                'icon' => 'fas fa-archive',
                'background' => 'linear-gradient(135deg, #7cf187 0%, #5ef153 100%)'
            ],
            'admin.audit_logs' => [
                'icon' => 'fas fa-archive',
                'background' => 'linear-gradient(135deg, #7cf187 0%, #5ef153 100%)'
            ],
            
            // Settings & System Configuration
            'admin.settings' => [
                'icon' => 'fas fa-cog',
                'background' => 'linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%)'
            ],
            'admin.countries' => [
                'icon' => 'fas fa-wrench',
                'background' => 'linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%)'
            ],
            'admin.airports' => [
                'icon' => 'fas fa-wrench',
                'background' => 'linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%)'
            ],
            'admin.harbors' => [
                'icon' => 'fas fa-wrench',
                'background' => 'linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%)'
            ],
            
            // Notifications
            'admin.notifications' => [
                'icon' => 'fas fa-bell',
                'background' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
            ],
            
            // Withdraw Revenues
            'admin.withdraw_revenues' => [
                'icon' => 'fas fa-money-check-alt',
                'background' => 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)'
            ]
        ];
    }
}
