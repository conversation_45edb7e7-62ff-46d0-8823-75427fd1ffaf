@extends('layouts.app')

@push('css')
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        <div class="card">
            <div class="d-flex py-6 px-6">
                <div class="ml-4">
                    @if (auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Create Advance'))
                        <a href="{{ route('admin.advances.create', ['type' => UserType::Client]) }}"
                            class="btn btn-primary me-2 mb-3 mb-sm-0">{{ __('Add Received Advance') }}</a>
                    @endif
                    @if (auth()->user()->IsSuperAdmin == 1 ||
                            (auth()->user()->type != UserType::Client && auth()->user()->can('Create Advance')))
                        <a href="{{ route('admin.advances.create', ['type' => UserType::Broker]) }}"
                            class="btn btn-info">{{ __('Add Paid Advance') }}
                        </a>
                    @endif
                </div>
                <div class="btn-group mx-4">
                    <a href="{{ route('admin.advances.exportAllToPdf') }}" class="btn btn-success" target="_blank"><i
                            class="fas fa-print"></i></a>

                </div>
            </div>
            <div class="row py-6 px-6">
                <div class="col-md-5">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;">{{ __('Search') }}</label>
                        <div class="col-md-10">
                            <input class="form-control" type="search" id="Search" placeholder="{{ __('Search...') }}"
                                id="html5-search-input" />
                        </div>
                    </div>
                </div>
                <div class="col-md-3"></div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class="">{{ __('show') }}</div>
                        <div class="mx-2">
                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="">{{ __('entities') }}</div>
                    </div>
                </div>
            </div>
            <div id="ListBodyDiv"></div>
        </div>
    </div>

    <div class="modal fade" id="addForm" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <form id="AddAdvanceForm" action="{{ route('admin.advances.store') }}" method="POST">
                    @csrf
                    <input type="hidden" name="_method" id="_method" value="">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel3">{{ __('Add New Advance') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="to_user_id" class="form-label">{{ __('User') }}</label>
                                <select name="to_user_id" class="select2" id="to_user_id">
                                    @foreach ($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                    @endforeach
                                </select>
                                @error('to_user_id')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-4">
                                <label for="amount" class="form-label">{{ __('Amount') }}</label>
                                <input type="number" id="amount" name="amount" class="form-control"
                                    value="{{ old('amount') }}" />
                                @error('amount')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">
                            {{ __('Close') }}
                        </button>
                        <button type="submit" class="btn btn-primary">{{ __('Submit') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/select2/select2.js') }}"></script>
    <script>
        $('.select2').select2({});

        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "{{ route('admin.advances.list') }}",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function deleteUser(id) {
            Swal.fire({
                title: "{{ __('Are you Sure?') }}",
                text: "{{ __('data will be deleted permenantly') }}",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "{{ __('Yes') }}",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    var token = $("#token").val();
                    $.post("{{ url('admin/advances/') }}" + "/" + id, {
                            _token: token,
                            _method: "DELETE"
                        },
                        function(data, status) {
                            if (status == "success") {
                                Swal.fire({
                                    icon: 'success',
                                    title: "{{ __('Deleted Successfully') }}",
                                    text: "{{ __('data has been deleted successfully') }}",
                                    customClass: {
                                        confirmButton: 'btn btn-success waves-effect waves-light'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: "{{ __('error') }}",
                                    text: "{{ __('something went wrong please try later') }}",
                                })
                            }
                            var pageNo = $("#PageNo").val();
                            LoadData(pageNo);
                        });
                }
            });

        }

        const formAuthentication = document.querySelector('#AddAdvanceForm');

        function showCreateModal() {
            $("#AddAdvanceForm").attr("action", "{{ route('admin.advances.store') }}");
            $("#_method").val("POST");
            $("#AddAdvanceForm")[0].reset();
            $(".modal-title").text("{{ __('Add New Advance') }}")
            $("#addForm").modal("show");
        }

        function showUpdateModal(id) {
            $.ajax({
                type: 'GET',
                url: "{{ url('admin/advances') }}" + `/${id}`,
                cache: false,
                dataType: "json",
                success: function(res) {
                    console.log(res);
                    if (res.success == true) {
                        res.data["is_default"] = res.data["is_default"] ? "1" : "0"
                        for (const key in res.data) {
                            if (res.data.hasOwnProperty(key)) {
                                const input = document.querySelector(`[name="${key}"]`);
                                if (input) {
                                    input.value = res.data[key];
                                }
                            }
                        }
                        $("#AddAdvanceForm").attr("action", "{{ url('admin/advances') }}" + `/${id}`);
                        $("#_method").val("PUT");
                        $(".modal-title").text("{{ __('Update Advance') }}")
                        $(".error").text("");
                        $("#addForm").modal("show");
                    } else {
                        alertError(res.error)
                    }
                },
                error: function(err) {
                    console.log(err)
                }
            });

        }
        $('#AddAdvanceForm').validate({
            errorElement: 'span',
            rules: {
                to_user_id: {
                    required: true,
                },
                amount: {
                    required: true,
                },
            },
            messages: {
                to_user_id: {
                    required: "{{ CrudMessage::isRequired(__('name')) }}",
                },
                amount: {
                    required: "{{ CrudMessage::isRequired(__('status')) }}",
                },
            },
            errorPlacement: function(error, element) {
                // Check if the element is a Select2 element
                if (element.hasClass('select2-hidden-accessible')) {
                    error.insertAfter(element.next(
                        '.select2-container')); // Place error after Select2 container
                } else {
                    error.insertAfter(element); // Default behavior for other inputs
                }
            },
            submitHandler: function(form) {
                $(".submitBtn").attr("disabled", "true")
                form.submit();
            }
        });
    </script>
@endpush
