<?php if($_Items != null && count($_Items) > 0): ?>
    <div class="card-datatable table-responsive">
        <table class="dt-advanced-search table">
            <thead class="table-light text-nowrap">
                <tr>
                    <th>#</th>
                    <th><?php echo e(__('Document Name')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="name" data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="name" data-dir="desc"></i>
                        </span>
                    </th>
                    
                    <th><?php echo e(__('End Date')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="end_date"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="end_date"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Status')); ?>

                    </th>
                    <th><?php echo e(__('Attachments')); ?>

                    </th>
                    <th><?php echo e(__('Actions')); ?>

                    </th>
                </tr>
            </thead>
            <tbody>
                <?php
                    $index = $startFrom;
                ?>
                <?php $__currentLoopData = $_Items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php echo e(++$index); ?>

                        </td>
                        <td>
                            <?php echo e($item->name); ?>

                        </td>
                        
                        <td>
                            <?php echo e($item->end_date ?? '-'); ?>

                        </td>
                        <td>
                            <?php echo e(__($item->remaining_days)); ?>

                        </td>
                        <td>
                            <?php $__empty_1 = true; $__currentLoopData = $item->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <a href="<?php echo e(asset($attachment->file_path)); ?>" download class="btn btn-sm btn-success"><i
                                        class="fas fa-download mb-2 mb-sm-0"></i></a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <span class="badge bg-danger"><?php echo e(__('Nothing')); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="d-flex order-actions">
                                <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Update Document')): ?>
                                    <a href="<?php echo e(route('admin.documents.edit', $item->id)); ?>" class="text-info"><i
                                            class='fas fa-edit'></i></a>
                                <?php endif; ?>
                                <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Delete Document')): ?>
                                    <a href="javascript:;" onclick="deleteUser('<?php echo e($item->id); ?>')"
                                        class="ms-3 text-danger"><i class='fas fa-trash'></i></a>
                                <?php endif; ?>
                            </div>
                        </td>

                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
    <br>
    <?php echo $__env->make('layouts.partials.pagination', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
    <div class="p-6">
        <div class="alert alert-warning text-center">
            <?php echo e(__('Sorry, No Data Found')); ?>

        </div>
    </div>
<?php endif; ?>

<input type="hidden" id="PageNo" value="<?php echo e($currentPage); ?>" />
<input type="hidden" id="OrderBy" value="<?php echo e($orderBy); ?>" />
<input type="hidden" id="Direction" value="<?php echo e($direction); ?>" />

<script></script>
<?php /**PATH E:\clearo\resources\views/admin/documents/list.blade.php ENDPATH**/ ?>