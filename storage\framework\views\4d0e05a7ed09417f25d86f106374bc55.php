<?php
    $lastPage = $totalPages;
    $startFrom = $startFrom == 0 ? 1 : $startFrom;

    $start = $currentPage - 2;
    $end = $currentPage + 2;

    if ($start < 1) {
        $start = 1;
        $end += 1;
    }

    if ($end >= $lastPage) {
        $end = $lastPage;
    }
?>

<nav class="px-3 mt-3 d-flex justify-content-between align-items-center" aria-label="Page navigation" style="flex-direction: column;">
    <ul class="pagination pagination-rounded">
        <?php if($currentPage > 1): ?>
            <li class="page-item" onclick="LoadData(<?php echo e($currentPage - 1); ?>)"><span class="page-link" href="#"><i
                        class="fas fa-arrow-right"></i></span></li>
        <?php else: ?>
            <li class="page-item disabled"><span class="page-link" href="#"><i
                        class="fas fa-arrow-right"></i></span></li>
        <?php endif; ?>

        <?php if($start > 1): ?>

            <li class="page-item" onclick="LoadData(1)"><span class="page-link" href="#">1</span></li>
            <?php if($currentPage > 4): ?>
                <li class="page-item disabled"><span class="page-link" href="#">...</span></li>
            <?php endif; ?>
        <?php endif; ?>

        <?php for($i = $start; $i <= $end; $i++): ?>
            <li class="page-item <?php echo e($currentPage == $i ? 'active' : ''); ?>" onclick="LoadData(<?php echo e($i); ?>)"><span
                    class="page-link" href="#"><?php echo e($i); ?></span></li>
        <?php endfor; ?>


        <?php if($end < $lastPage): ?>

            <?php if($currentPage + 3 < $lastPage): ?>
                <li class="page-item disabled"><span class="page-link" href="#">...</span></li>
            <?php endif; ?>
            <li class="page-item" onclick="LoadData(<?php echo e($lastPage); ?>)"><span class="page-link"
                    href="#"><?php echo e($lastPage); ?></span></li>
        <?php endif; ?>

        <?php if($currentPage < $lastPage): ?>
            <li class="page-item" onclick="LoadData(<?php echo e($currentPage + 1); ?>)"><span class="page-link" href="#"><i
                        class="fas fa-arrow-left"></i></span></li>
        <?php else: ?>
            <li class="page-item disabled"><span class="page-link" href="#"><i
                        class="fas fa-arrow-left"></i></span></li>
        <?php endif; ?>

    </ul>

    <div>
        <p class="text-muted">
            <?php echo e(__('showing from')); ?><?php echo e($startFrom); ?> <?php echo e(__('to')); ?> <?php echo e($endTo); ?>

            <?php echo e(__('of total')); ?> <?php echo e($totalCount); ?>

        </p>
    </div>
</nav>


<?php /**PATH E:\clearo\resources\views/layouts/partials/pagination.blade.php ENDPATH**/ ?>