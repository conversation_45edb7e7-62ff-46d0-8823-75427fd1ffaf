<?php if($_Items != null && count($_Items) > 0): ?>
    <div class="card-datatable table-responsive">
        <table class="dt-advanced-search table">
            <thead class="table-light text-nowrap">
                <tr>
                    <th>#</th>
                    <th><?php echo e(__('Name')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="name" data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="name" data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Country')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="country_id"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="country_id"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Actions')); ?>

                    </th>
                </tr>
            </thead>
            <tbody>
                <?php
                    $index = $startFrom;
                ?>
                <?php $__currentLoopData = $_Items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php echo e(++$index); ?>

                        </td>
                        <td>
                            <?php echo e($item->name); ?>

                        </td>
                        <td>
                            <?php echo e($item->country->name); ?>

                        </td>
                        <td>
                            <div class="d-flex order-actions">
                                <a href="javascript:;" onclick="showUpdateModal('<?php echo e($item->id); ?>')"
                                    class="text-info"><i class='fas fa-edit'></i></a>
                                <a href="javascript:;" onclick="deleteUser('<?php echo e($item->id); ?>')"
                                    class="ms-3 text-danger"><i class='fas fa-trash'></i></a>
                            </div>
                        </td>

                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
    <br>
    <?php echo $__env->make('layouts.partials.pagination', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
    <div class="p-6">
        <div class="alert alert-warning text-center">
            <?php echo e(__('Sorry, No Data Found')); ?>

        </div>
    </div>
<?php endif; ?>

<input type="hidden" id="PageNo" value="<?php echo e($currentPage); ?>" />
<input type="hidden" id="OrderBy" value="<?php echo e($orderBy); ?>" />
<input type="hidden" id="Direction" value="<?php echo e($direction); ?>" />

<script></script>
<?php /**PATH E:\clearo\resources\views/admin/intialization/harbors/list.blade.php ENDPATH**/ ?>