<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
    <style>
        .ul-list {
            padding: .3rem 1.3rem;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="container-xxl flex-grow-1 container-p-y">
        <?php echo $__env->make('components.page-title', ['title' => $title], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php echo $__env->make('admin.financial_years.partials.filter', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="row g-4 my-6">
            <?php echo $__env->make('admin.financial_years.months', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script>
        function LoadData(pageNo) {
            var search = $("#Search").val();
            var rowsPerPage = 15;
            var token = $("#token").val();
            var financial_year_id = $("#financial_year_id").val();
            var start_from = $("#start_from").val();
            var end_to = $("#end_to").val();
            console.log(start_from);
            console.log(end_to);
            console.log(financial_year_id);
            $.ajax({
                type: 'POST',
                url: "<?php echo e(route('admin.financial_months.list')); ?>",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    financial_year_id,
                    start_from,
                    end_to,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/financial_years/index.blade.php ENDPATH**/ ?>