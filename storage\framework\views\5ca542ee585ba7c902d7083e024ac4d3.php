
    <?php echo $__env->make("layouts.export-partials.export_header", ['title' => __('Advances List')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    

    <table class="custom-table">
        <thead>
            <tr>
                <th><?php echo e(__('Date')); ?></th>
                <?php if(is_admin()): ?>
                    <th><?php echo e(__('From User')); ?></th>
                    <th><?php echo e(__('To User')); ?></th>
                <?php endif; ?>

                <th><?php echo e(__('Amount')); ?></th>
            </tr>
        </thead>
        <tbody>

            <?php $__currentLoopData = $advances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($item->date); ?></td>
                    <?php if(is_admin()): ?>
                        <td><?php echo e(@$item->fromUser->name ?? '-'); ?></td>
                        <td><?php echo e(@$item->toUser->name ?? '-'); ?></td>
                    <?php endif; ?>
                    <td><?php echo e($item->amount); ?></td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
<?php echo $__env->make("layouts.export-partials.export_footer", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php /**PATH E:\clearo\resources\views/admin/advances/export_pdf.blade.php ENDPATH**/ ?>