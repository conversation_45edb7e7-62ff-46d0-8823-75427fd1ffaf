<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="container-xxl flex-grow-1 container-p-y">
        <?php echo $__env->make('components.page-title', ['title' => $title], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="card">
            <div class="d-flex py-6 px-6">
                <div class="ml-4">
                    <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Create Advance')): ?>
                        <a href="<?php echo e(route('admin.advances.create', ['type' => UserType::Client])); ?>"
                            class="btn btn-primary me-2 mb-3 mb-sm-0"><?php echo e(__('Add Received Advance')); ?></a>
                    <?php endif; ?>
                    <?php if(auth()->user()->IsSuperAdmin == 1 ||
                            (auth()->user()->type != UserType::Client && auth()->user()->can('Create Advance'))): ?>
                        <a href="<?php echo e(route('admin.advances.create', ['type' => UserType::Broker])); ?>"
                            class="btn btn-info"><?php echo e(__('Add Paid Advance')); ?>

                        </a>
                    <?php endif; ?>
                </div>
                <div class="btn-group mx-4">
                    <a href="<?php echo e(route('admin.advances.exportAllToPdf')); ?>" class="btn btn-success" target="_blank"><i
                            class="fas fa-print"></i></a>

                </div>
            </div>
            <div class="row py-6 px-6">
                <div class="col-md-5">
                    <div class="row">
                        <label for="html5-search-input" class="col-md-2 col-form-label"
                            style="text-wrap:nowrap;"><?php echo e(__('Search')); ?></label>
                        <div class="col-md-10">
                            <input class="form-control" type="search" id="Search" placeholder="<?php echo e(__('Search...')); ?>"
                                id="html5-search-input" />
                        </div>
                    </div>
                </div>
                <div class="col-md-3"></div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class=""><?php echo e(__('show')); ?></div>
                        <div class="mx-2">
                            <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class=""><?php echo e(__('entities')); ?></div>
                    </div>
                </div>
            </div>
            <div id="ListBodyDiv"></div>
        </div>
    </div>

    <div class="modal fade" id="addForm" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <form id="AddAdvanceForm" action="<?php echo e(route('admin.advances.store')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="_method" id="_method" value="">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel3"><?php echo e(__('Add New Advance')); ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="to_user_id" class="form-label"><?php echo e(__('User')); ?></label>
                                <select name="to_user_id" class="select2" id="to_user_id">
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['to_user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6 mb-4">
                                <label for="amount" class="form-label"><?php echo e(__('Amount')); ?></label>
                                <input type="number" id="amount" name="amount" class="form-control"
                                    value="<?php echo e(old('amount')); ?>" />
                                <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">
                            <?php echo e(__('Close')); ?>

                        </button>
                        <button type="submit" class="btn btn-primary"><?php echo e(__('Submit')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script>
        $('.select2').select2({});

        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(route('admin.advances.list')); ?>",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function deleteUser(id) {
            Swal.fire({
                title: "<?php echo e(__('Are you Sure?')); ?>",
                text: "<?php echo e(__('data will be deleted permenantly')); ?>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "<?php echo e(__('Yes')); ?>",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    var token = $("#token").val();
                    $.post("<?php echo e(url('admin/advances/')); ?>" + "/" + id, {
                            _token: token,
                            _method: "DELETE"
                        },
                        function(data, status) {
                            if (status == "success") {
                                Swal.fire({
                                    icon: 'success',
                                    title: "<?php echo e(__('Deleted Successfully')); ?>",
                                    text: "<?php echo e(__('data has been deleted successfully')); ?>",
                                    customClass: {
                                        confirmButton: 'btn btn-success waves-effect waves-light'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: "<?php echo e(__('error')); ?>",
                                    text: "<?php echo e(__('something went wrong please try later')); ?>",
                                })
                            }
                            var pageNo = $("#PageNo").val();
                            LoadData(pageNo);
                        });
                }
            });

        }

        const formAuthentication = document.querySelector('#AddAdvanceForm');

        function showCreateModal() {
            $("#AddAdvanceForm").attr("action", "<?php echo e(route('admin.advances.store')); ?>");
            $("#_method").val("POST");
            $("#AddAdvanceForm")[0].reset();
            $(".modal-title").text("<?php echo e(__('Add New Advance')); ?>")
            $("#addForm").modal("show");
        }

        function showUpdateModal(id) {
            $.ajax({
                type: 'GET',
                url: "<?php echo e(url('admin/advances')); ?>" + `/${id}`,
                cache: false,
                dataType: "json",
                success: function(res) {
                    console.log(res);
                    if (res.success == true) {
                        res.data["is_default"] = res.data["is_default"] ? "1" : "0"
                        for (const key in res.data) {
                            if (res.data.hasOwnProperty(key)) {
                                const input = document.querySelector(`[name="${key}"]`);
                                if (input) {
                                    input.value = res.data[key];
                                }
                            }
                        }
                        $("#AddAdvanceForm").attr("action", "<?php echo e(url('admin/advances')); ?>" + `/${id}`);
                        $("#_method").val("PUT");
                        $(".modal-title").text("<?php echo e(__('Update Advance')); ?>")
                        $(".error").text("");
                        $("#addForm").modal("show");
                    } else {
                        alertError(res.error)
                    }
                },
                error: function(err) {
                    console.log(err)
                }
            });

        }
        $('#AddAdvanceForm').validate({
            errorElement: 'span',
            rules: {
                to_user_id: {
                    required: true,
                },
                amount: {
                    required: true,
                },
            },
            messages: {
                to_user_id: {
                    required: "<?php echo e(CrudMessage::isRequired(__('name'))); ?>",
                },
                amount: {
                    required: "<?php echo e(CrudMessage::isRequired(__('status'))); ?>",
                },
            },
            errorPlacement: function(error, element) {
                // Check if the element is a Select2 element
                if (element.hasClass('select2-hidden-accessible')) {
                    error.insertAfter(element.next(
                        '.select2-container')); // Place error after Select2 container
                } else {
                    error.insertAfter(element); // Default behavior for other inputs
                }
            },
            submitHandler: function(form) {
                $(".submitBtn").attr("disabled", "true")
                form.submit();
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/advances/index.blade.php ENDPATH**/ ?>