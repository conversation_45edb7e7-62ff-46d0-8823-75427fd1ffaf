<?php
    $r = \Route::current()->getAction();
    $route = isset($r['as']) ? $r['as'] : '';
?>
<aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
    <div class="app-brand demo">
        <a href="<?php echo e(route('admin.dashboard')); ?>" class="app-brand-link">
            <span style="width: 90px;">
                <img style="max-width: 90px;"
                    src="<?php echo e(asset(@get_setting('logo') && get_setting('logo') != '' ? get_setting('logo') : 'theme/assets/img/favicon.ico')); ?>"
                    alt="logo">
                
            </span>
            <span class="app-brand-text demo menu-text fw-bold"><?php echo e(get_site_name()); ?></span>
        </a>

        <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
            <i class="ti menu-toggle-icon d-none d-xl-block align-middle"></i>
            <i class="ti ti-x d-block d-xl-none ti-md align-middle"></i>
        </a>
    </div>

    <div class="menu-inner-shadow"></div>

    <ul class="menu-inner py-1">
        <?php if(is_admin() || is_client() || is_broker() || is_system_employee() || is_sub_client()): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.custom_shippings') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.custom_shippings.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-cubes"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Shipment Management')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1): ?>
            <li
                class="menu-item <?php echo e(Str::startsWith($route, 'admin.custom_shipping_invoices') &&
                (request()->query('type') == UserType::Client->value || request()->query('type') == UserType::Broker->value)
                    ? 'active open'
                    : ''); ?>">
                <a href="javascript:void(0);" class="menu-link menu-toggle" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Invoice Tracking')); ?></div>
                </a>

                <ul class="menu-sub">
                    
                    <li
                        class="menu-item <?php echo e(Str::startsWith($route, 'admin.custom_shipping_invoices') && request()->query('type') == UserType::Client->value ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.custom_shipping_invoices.index', ['type' => UserType::Client])); ?>"
                            class="menu-link">
                            <div><?php echo e(__('Clients Invoices')); ?></div>
                        </a>
                    </li>
                    
                    
                    <li
                        class="menu-item <?php echo e(Str::startsWith($route, 'admin.custom_shipping_invoices') && request()->query('type') == UserType::Broker->value ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.custom_shipping_invoices.index', ['type' => UserType::Broker])); ?>"
                            class="menu-link">
                            <div><?php echo e(__('Workers Invoices')); ?></div>
                        </a>
                    </li>
                    
                </ul>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->canAny(['View Shipping Invoices', 'View Related Shipping Invoices']) &&
                (is_client() || is_sub_client())): ?>
            <li
                class="menu-item <?php echo e(Str::startsWith($route, 'admin.custom_shipping_invoices') && request()->query('type') == UserType::Client->value ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.custom_shipping_invoices.index', ['type' => UserType::Client])); ?>"
                    class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Shipping Invoices')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->canAny(['View Shipping Invoices', 'View Related Shipping Invoices']) && is_broker()): ?>
            <li
                class="menu-item <?php echo e(Str::startsWith($route, 'admin.custom_shipping_invoices') && request()->query('type') == UserType::Broker->value ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.custom_shipping_invoices.index', ['type' => UserType::Broker])); ?>"
                    class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Shipping Invoices')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1): ?>
            <li
                class="menu-item <?php echo e(Str::startsWith($route, 'admin.financial_months') || Str::startsWith($route, 'admin.withdraw') ? 'active open' : ''); ?>">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);">
                            <i class="far fa-calendar-alt"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Monthly Accounting')); ?></div>
                </a>
                <ul class="menu-sub">
                    <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.financial_months') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.financial_months.index')); ?>" class="menu-link">
                            <div><?php echo e(__('Financial Months')); ?></div>
                        </a>
                    </li>
                    <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.withdraw') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.withdraw.index')); ?>" class="menu-link">
                            <div><?php echo e(__('Withdraw Revenue')); ?></div>
                        </a>
                    </li>
                </ul>
            </li>
        <?php endif; ?>

        <?php if(auth()->user()->IsSuperAdmin == 1 ||
                auth()->user()->can('View Expenses') ||
                auth()->user()->can('View Purchases') ||
                auth()->user()->can('View Revenues')): ?>
            <li
                class="menu-item <?php echo e(Str::startsWith($route, 'admin.expenses') || Str::startsWith($route, 'admin.purchases') || Str::startsWith($route, 'admin.revenues') ? 'active open' : ''); ?>">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Revenues & Expenses')); ?></div>
                </a>
                <ul class="menu-sub">
                    <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View_Expenses')): ?>
                        <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.expenses') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('admin.expenses.index')); ?>" class="menu-link">
                                <div><?php echo e(__('Expenses')); ?></div>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View_Purchases')): ?>
                        <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.purchases') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('admin.purchases.index')); ?>" class="menu-link">
                                <div><?php echo e(__('Purchases')); ?></div>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View_Revenues')): ?>
                        <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.revenues') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('admin.revenues.index')); ?>" class="menu-link">
                                <div><?php echo e(__('Revenues')); ?></div>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1 ||
                (auth()->user()->can('View Advances') || auth()->user()->can('View Related Advances'))): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.advances') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.advances.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #553c9a 0%, #8b5cf6 100%);">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Payment Overview')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1): ?>
            <li
                class="menu-item <?php echo e(Str::startsWith($route, 'admin.client_debits') || Str::startsWith($route, 'admin.worker_dues') ? 'active open' : ''); ?>">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);">
                            <i class="fas fa-wallet"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Financial Dues Management')); ?></div>
                </a>
                <ul class="menu-sub">
                    <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.client_debits') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.client_debits.index')); ?>" class="menu-link">
                            <div class="menu-icon-wrapper">
                                <div class="menu-icon-circle" style="background: linear-gradient(135deg, #2c7a7b 0%, #4fd1c7 100%);">
                                    <i class="fas fa-wallet"></i>
                                </div>
                            </div>
                            <div><?php echo e(__('Clients')); ?></div>
                        </a>
                    </li>
                    <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.worker_dues') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.worker_dues.index')); ?>" class="menu-link">
                            <div class="menu-icon-wrapper">
                                <div class="menu-icon-circle" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);">
                                    <i class="fas fa-wallet"></i>
                                </div>
                            </div>
                            <div><?php echo e(__('Brokers')); ?></div>
                        </a>
                    </li>
                </ul>
            </li>
        <?php endif; ?>
        
        <?php if(auth()->user()->can('View Client Debits') || auth()->user()->can('View Related Client Debits')): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.client_debits') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.client_debits.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);">
                            <i class="fas fa-wallet"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Outstanding Balance')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View Users')): ?>
            <li
                class="menu-item
            <?php echo e(Str::startsWith($route, 'admin.users') && request()->query('type') == UserType::Broker->value ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.users.index', ['type' => UserType::Broker])); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);">
                            <i class="fas fa-user-shield"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Borker Operations')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View Clients')): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.clients') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.clients.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #2c7a7b 0%, #4fd1c7 100%);">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Client Operations')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View Companies')): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.companies') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.companies.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #71e5e0 0%, #e980a2 100%);">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Companies')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->can('View Worker Dues') || auth()->user()->can('View Related Worker Dues')): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.worker_dues') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.worker_dues.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);">
                            <i class="fas fa-wallet"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Financial Dues Management')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View Documents')): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.documents') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.documents.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);">
                            <i class="far fa-file-alt"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Document Archive')); ?></div>
                </a>
            </li>
        <?php endif; ?>

        <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View Hs Sheet')): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.hs_sheet') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.hs_sheet.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #f3c382 0%, #ef7e59 100%);">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Tarrif Lookup')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <!-- Page -->
        <?php if(auth()->user()->IsSuperAdmin == 1 || (auth()->user()->can('View Users') || auth()->user()->can('View Roles'))): ?>
            <li
                class="menu-item <?php echo e((Str::startsWith($route, 'admin.users') && request()->query('type') == null) || Str::startsWith($route, 'admin.roles') ? 'active open' : ''); ?>">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #f36bcd 0%, #f5e57c 100%);">
                            <i class="fas fa-users-cog"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Users and Roles')); ?></div>
                </a>
                <ul class="menu-sub">
                    <li
                        class="menu-item <?php echo e(Str::startsWith($route, 'admin.users') && request()->query('type') == null ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="menu-link">
                            <div><?php echo e(__('Users List')); ?></div>
                        </a>
                    </li>
                    <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.roles') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.roles.index')); ?>" class="menu-link">
                            <div><?php echo e(__('Roles List')); ?></div>
                        </a>
                    </li>
                </ul>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1): ?>
            <li
                class="menu-item <?php echo e(Str::startsWith($route, 'admin.authentication_logs') || Str::startsWith($route, 'admin.audit_logs') ? 'active open' : ''); ?>">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #7cf187 0%, #5ef153 100%);">
                            <i class="fas fa-archive"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('Logs')); ?></div>
                </a>
                <ul class="menu-sub">
                    <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.authentication_logs') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.authentication_logs.index')); ?>" class="menu-link">
                            <div><?php echo e(__('Authentication Logs')); ?></div>
                        </a>
                    </li>
                    <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.audit_logs') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.audit_logs.index')); ?>" class="menu-link">
                            <div><?php echo e(__('Audit Logs')); ?></div>
                        </a>
                    </li>
                </ul>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View General Settings')): ?>
            <li class="menu-item <?php echo e(Str::startsWith($route, 'admin.settings') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.settings.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-cogs"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('General Settings')); ?></div>
                </a>
            </li>
        <?php endif; ?>
        <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View Intialization Settings')): ?>
            <li
                class="menu-item <?php echo e(Str::startsWith($route, 'admin.countries') || Str::startsWith($route, 'admin.airports') || Str::startsWith($route, 'admin.harbors') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('admin.countries.index')); ?>" class="menu-link">
                    <div class="menu-icon-wrapper">
                        <div class="menu-icon-circle" style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);">
                            <i class="fas fa-wrench"></i>
                        </div>
                    </div>
                    <div><?php echo e(__('System Configuration')); ?></div>
                </a>
            </li>
        <?php endif; ?>













    </ul>
</aside>
<?php /**PATH E:\clearo\resources\views/layouts/partials/sidebar.blade.php ENDPATH**/ ?>