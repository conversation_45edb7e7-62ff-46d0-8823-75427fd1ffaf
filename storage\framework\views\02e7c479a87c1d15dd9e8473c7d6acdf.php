<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/css/custom-arabic-styles.css')); ?>" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="container-xxl flex-grow-1 container-p-y">
        <?php echo $__env->make('components.page-title', ['title' => $title], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="row">
            <div class="col-md-8">
                <div class="card mb-6">
                    <form class="card-body" action="<?php echo e(route('admin.settings.update')); ?>" method="POST"
                        enctype="multipart/form-data" id="CreateForm">
                        <?php echo csrf_field(); ?>
                        <div class="row g-6">
                            <div class="col-md-6">
                                <label class="form-label" for="logo"><?php echo e(__('Logo')); ?></label>
                                <input class="form-control" name="logo" type="file" id="logo" accept="image/*"
                                    onchange="loadFile(event, 'logoPreview')" />
                                <?php $__errorArgs = ['logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="favicon"><?php echo e(__('Favicon')); ?></label>
                                <input class="form-control" type="file" id="favicon" name="favicon" accept="image/*"
                                    onchange="loadFile(event, 'faviconPreview')" />
                                <?php $__errorArgs = ['favicon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="site_name"><?php echo e(__('Site Name')); ?></label>
                                <input type="text" id="site_name" name="site_name" class="form-control"
                                    value="<?php echo e($items['site_name']); ?>" />
                                <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="company_name"><?php echo e(__('Company Name')); ?></label>
                                <input type="text" id="company_name" name="company_name" class="form-control"
                                    value="<?php echo e($items['company_name']); ?>" />
                                <?php $__errorArgs = ['company_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="owner_name"><?php echo e(__('Owner Name')); ?></label>
                                <input type="text" id="owner_name" name="owner_name" class="form-control"
                                    value="<?php echo e($items['owner_name']); ?>" />
                                <?php $__errorArgs = ['owner_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="email"><?php echo e(__('Email')); ?></label>
                                <input type="text" id="email" name="email" class="form-control"
                                    value="<?php echo e($items['email']); ?>" />
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="phone"><?php echo e(__('phone')); ?></label>
                                <input type="text" id="phone" name="phone" class="form-control"
                                    value="<?php echo e($items['phone']); ?>" />
                                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="other_phone"><?php echo e(__('another phone')); ?></label>
                                <input type="text" id="other_phone" name="other_phone" class="form-control"
                                    value="<?php echo e($items['other_phone']); ?>" />
                                <?php $__errorArgs = ['other_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="location"><?php echo e(__('location')); ?></label>
                                <input type="text" id="location" name="location" class="form-control"
                                    value="<?php echo e($items['location']); ?>" />
                                <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="facebook"><?php echo e(__('facebook')); ?></label>
                                <input type="text" id="facebook" name="facebook" class="form-control"
                                    value="<?php echo e($items['facebook']); ?>" />
                                <?php $__errorArgs = ['facebook'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="whatsapp"><?php echo e(__('whatsapp')); ?></label>
                                <input type="text" id="whatsapp" name="whatsapp" class="form-control"
                                    value="<?php echo e($items['whatsapp']); ?>" />
                                <?php $__errorArgs = ['whatsapp'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="google_map"><?php echo e(__('google map')); ?></label>
                                <input type="text" id="google_map" name="google_map" class="form-control"
                                    value="<?php echo e($items['google_map']); ?>" />
                                <?php $__errorArgs = ['google_map'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-12">
                                <label class="form-label" for="description"><?php echo e(__('description')); ?></label>
                                <input type="text" id="description" name="description" class="form-control"
                                    value="<?php echo e($items['description']); ?>" />
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>


                        </div>

                        <div class="pt-6">
                            <button type="submit" class="btn btn-primary me-4"><?php echo e(__('Submit')); ?></button>
                            <a href="<?php echo e(route('admin.roles.index')); ?>"
                                class="btn btn-label-secondary"><?php echo e(__('Cancel')); ?></a>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <h6 class="text-center my-4"><?php echo e(__('Site Logo')); ?></h6>
                    <div class="d-flex justify-content-center mb-6">
                        <img src="<?php echo e(asset(isset($items['logo']) && $items['logo'] != '' ? $items['logo'] : 'theme/assets/img/default-logo.png')); ?>"
                            alt="<?php echo e($items['logo']); ?>" id="logoPreview" width="90%">
                    </div>
                    <br>
                    <hr>
                    <h6 class="text-center my-2"><?php echo e(__('Favicon')); ?></h6>
                    <div class="d-flex justify-content-center mb-6">
                        <img src="<?php echo e(asset(isset($items['favicon']) && $items['favicon'] != '' ? $items['favicon'] : 'theme/assets/img/favicon.ico')); ?>"
                            alt="<?php echo e($items['favicon']); ?>" id="faviconPreview" width="10%">
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script>
        $('.select2').select2({});

        var loadFile = function(event, id) {
            var output = document.getElementById(id);
            output.src = URL.createObjectURL(event.target.files[0]);
            output.onload = function() {
                URL.revokeObjectURL(output.src) // free memory
            }
        };
    </script>
    <script src="<?php echo e(asset('theme/assets/js/custom-arabic-translations.js')); ?>"></script>
    <script>
        window.appLocale = '<?php echo e(app()->getLocale()); ?>';
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>