<?php if($_Items != null && count($_Items) > 0): ?>
    <div class="card-datatable table-responsive">
        <table class="dt-advanced-search table">
            <thead class="table-light text-nowrap">
                <tr>
                    <th>#</th>
                    <th><?php echo e(__('Date')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="created_at"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="created_at"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Company Name')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="company_name"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="company_name"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Invoice Number')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="invoice_number"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="invoice_number"
                                data-dir="desc"></i>
                        </span>
                    </th>

                    <th><?php echo e(__('Policy Number')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="policy_number"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="policy_number"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <?php if(is_admin()): ?>
                        <th><?php echo e(__('Shipping Code')); ?>

                            <span class="arrows-container">
                                <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="code"
                                    data-dir="asc"></i>
                                <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="code"
                                    data-dir="desc"></i>
                            </span>
                        </th>
                    <?php endif; ?>
                    <th><?php echo e(__('Packages Count')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="packages_count"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="packages_count"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Total Weight')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="total_weight"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="total_weight"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Exported From')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="from_country"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="from_country"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    
                    <th><?php echo e(__('Total Expense')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="total_amount"
                                data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="total_amount"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Attachments')); ?>

                    </th>
                    <th><?php echo e(__('Actions')); ?>

                    </th>
                </tr>
            </thead>
            <tbody>
                <?php
                    $index = $startFrom;
                ?>
                <?php $__currentLoopData = $_Items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php echo e(++$index); ?>

                        </td>
                        <td class="text-nowrap">
                            <?php echo e($item->created_at->toDateString()); ?>

                        </td>
                        <td>
                            <?php echo e(@$item->customShipping->company->name); ?>

                        </td>
                        <td>
                            <?php echo e($item->invoice_number); ?>

                        </td>
                        <td class="">
                            <?php echo e($item->customShipping?->policy_number); ?>

                        </td>
                        <?php if(is_admin()): ?>
                            <td class="">
                                <?php echo e($item->customShipping?->code); ?>

                            </td>
                        <?php endif; ?>
                        <td class="">
                            <?php echo e($item->customShipping?->packages_count); ?>

                        </td>
                        <td class="">
                            <?php echo e($item->customShipping?->total_weight); ?>

                        </td>
                        <td class="">
                            <?php echo e(@$item->customShipping?->fromCountry->name); ?>

                        </td>
                        <td>
                            <?php echo e($item->total_amount); ?>

                        </td>
                        <td>
                            <div class="d-flex order-actions">
                                <?php $__empty_1 = true; $__currentLoopData = $item->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <a href="<?php echo e(asset($attachment->file_path)); ?>" download
                                        class="btn btn-sm btn-success mb-2 mb-sm-0"><i class="fas fa-download"></i></a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <span class="badge bg-danger"><?php echo e(__('Nothing')); ?></span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex order-actions">
                                <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Update Shipping Invoice')): ?>
                                    <a href="<?php echo e(route('admin.custom_shipping_invoices.edit', $item->id)); ?>"
                                        class="ms-3 btn btn-sm btn-info waves-effect waves-light text-nowrap">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                <?php endif; ?>
                                <a href="<?php echo e(route('admin.custom_shipping_invoices.exportPdf', $item->id)); ?>"
                                    target="_blank"
                                    class="ms-3 btn btn-sm btn-success waves-effect waves-light text-nowrap">
                                    <i class="fas fa-print"></i>
                                </a>
                                <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('Delete Shipping Invoice')): ?>
                                    <a href="javascript:;" onclick="deleteRecord('<?php echo e($item->id); ?>')"
                                        class="ms-3 btn btn-sm btn-danger waves-effect waves-light text-nowrap">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                <?php endif; ?>

                            </div>
                        </td>

                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
    <br>
    <?php echo $__env->make('layouts.partials.pagination', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
    <div class="p-6">
        <div class="alert alert-warning text-center">
            <?php echo e(__('Sorry, No Data Found')); ?>

        </div>
    </div>
<?php endif; ?>

<input type="hidden" id="PageNo" value="<?php echo e($currentPage); ?>" />
<input type="hidden" id="OrderBy" value="<?php echo e($orderBy); ?>" />
<input type="hidden" id="Direction" value="<?php echo e($direction); ?>" />

<script></script>
<?php /**PATH E:\clearo\resources\views/admin/shipping_invoices/user_invoices_list.blade.php ENDPATH**/ ?>