@extends('layouts.app')
@push('css')
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('theme/assets/css/custom-arabic-styles.css') }}" />
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        <div class="row">
            <div class="col-md-8">
                <div class="card mb-6">
                    <form class="card-body" action="{{ route('admin.settings.update') }}" method="POST"
                        enctype="multipart/form-data" id="CreateForm">
                        @csrf
                        <div class="row g-6">
                            <div class="col-md-6">
                                <label class="form-label" for="logo">{{ __('Logo') }}</label>
                                <input class="form-control" name="logo" type="file" id="logo" accept="image/*"
                                    onchange="loadFile(event, 'logoPreview')" />
                                @error('logo')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="favicon">{{ __('Favicon') }}</label>
                                <input class="form-control" type="file" id="favicon" name="favicon" accept="image/*"
                                    onchange="loadFile(event, 'faviconPreview')" />
                                @error('favicon')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="site_name">{{ __('Site Name') }}</label>
                                <input type="text" id="site_name" name="site_name" class="form-control"
                                    value="{{ $items['site_name'] }}" />
                                @error('site_name')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="company_name">{{ __('Company Name') }}</label>
                                <input type="text" id="company_name" name="company_name" class="form-control"
                                    value="{{ $items['company_name'] }}" />
                                @error('company_name')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="owner_name">{{ __('Owner Name') }}</label>
                                <input type="text" id="owner_name" name="owner_name" class="form-control"
                                    value="{{ $items['owner_name'] }}" />
                                @error('owner_name')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="email">{{ __('Email') }}</label>
                                <input type="text" id="email" name="email" class="form-control"
                                    value="{{ $items['email'] }}" />
                                @error('email')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="phone">{{ __('phone') }}</label>
                                <input type="text" id="phone" name="phone" class="form-control"
                                    value="{{ $items['phone'] }}" />
                                @error('phone')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="other_phone">{{ __('another phone') }}</label>
                                <input type="text" id="other_phone" name="other_phone" class="form-control"
                                    value="{{ $items['other_phone'] }}" />
                                @error('other_phone')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="location">{{ __('location') }}</label>
                                <input type="text" id="location" name="location" class="form-control"
                                    value="{{ $items['location'] }}" />
                                @error('location')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="facebook">{{ __('facebook') }}</label>
                                <input type="text" id="facebook" name="facebook" class="form-control"
                                    value="{{ $items['facebook'] }}" />
                                @error('facebook')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="whatsapp">{{ __('whatsapp') }}</label>
                                <input type="text" id="whatsapp" name="whatsapp" class="form-control"
                                    value="{{ $items['whatsapp'] }}" />
                                @error('whatsapp')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="google_map">{{ __('google map') }}</label>
                                <input type="text" id="google_map" name="google_map" class="form-control"
                                    value="{{ $items['google_map'] }}" />
                                @error('google_map')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="col-md-12">
                                <label class="form-label" for="description">{{ __('description') }}</label>
                                <input type="text" id="description" name="description" class="form-control"
                                    value="{{ $items['description'] }}" />
                                @error('description')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>


                        </div>

                        <div class="pt-6">
                            <button type="submit" class="btn btn-primary me-4">{{ __('Submit') }}</button>
                            <a href="{{ route('admin.roles.index') }}"
                                class="btn btn-label-secondary">{{ __('Cancel') }}</a>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <h6 class="text-center my-4">{{ __('Site Logo') }}</h6>
                    <div class="d-flex justify-content-center mb-6">
                        <img src="{{ asset(isset($items['logo']) && $items['logo'] != '' ? $items['logo'] : 'theme/assets/img/default-logo.png') }}"
                            alt="{{ $items['logo'] }}" id="logoPreview" width="90%">
                    </div>
                    <br>
                    <hr>
                    <h6 class="text-center my-2">{{ __('Favicon') }}</h6>
                    <div class="d-flex justify-content-center mb-6">
                        <img src="{{ asset(isset($items['favicon']) && $items['favicon'] != '' ? $items['favicon'] : 'theme/assets/img/favicon.ico') }}"
                            alt="{{ $items['favicon'] }}" id="faviconPreview" width="10%">
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('theme/assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script>
        $('.select2').select2({});

        var loadFile = function(event, id) {
            var output = document.getElementById(id);
            output.src = URL.createObjectURL(event.target.files[0]);
            output.onload = function() {
                URL.revokeObjectURL(output.src) // free memory
            }
        };
    </script>
    <script src="{{ asset('theme/assets/js/custom-arabic-translations.js') }}"></script>
    <script>
        window.appLocale = '{{ app()->getLocale() }}';
    </script>
@endpush
