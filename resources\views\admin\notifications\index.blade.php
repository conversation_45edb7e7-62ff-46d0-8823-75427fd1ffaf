@extends('layouts.app')
@push('css')
    <style>
        .unread {
            background: #fbfbfb;
        }
    </style>
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        <div id="ListBodyDiv">

        </div>
    </div>
@endsection

@push('js')
    <script>
        function LoadData(pageNo) {
            $.ajax({
                type: 'POST',
                url: "{{ route('admin.notifications.list') }}",
                data: {
                    PageNo: pageNo,
                    _token: $("#token").val()
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });
    </script>
@endpush
