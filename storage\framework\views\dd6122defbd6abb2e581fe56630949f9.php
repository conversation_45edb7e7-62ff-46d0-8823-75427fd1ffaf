<?php if($_Items != null && count($_Items) > 0): ?>
    <div class="card-datatable table-responsive">
        <table class="dt-advanced-search table">
            <thead class="table-light text-nowrap">
                <tr>
                    <th>#</th>
                    <th><?php echo e(__('Hs Code')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="hs_code" data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="hs_code"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Duty')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="duty" data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="duty"
                                data-dir="desc"></i>
                        </span>
                    </th>
                    <?php if(app()->getLocale() == 'en'): ?>
                        <th><?php echo e(__('English Name')); ?>

                            <span class="arrows-container">
                                <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="name_en"
                                    data-dir="asc"></i>
                                <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="name_en"
                                    data-dir="desc"></i>
                            </span>
                        </th>
                    <?php else: ?>
                        <th><?php echo e(__('Arabic Name')); ?>

                            <span class="arrows-container">
                                <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="name_ar"
                                    data-dir="asc"></i>
                                <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="name_ar"
                                    data-dir="desc"></i>
                            </span>
                        </th>
                    <?php endif; ?>
                    
                    
                </tr>
            </thead>
            <tbody>
                <?php
                    $index = $startFrom;
                ?>
                <?php $__currentLoopData = $_Items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php echo e(++$index); ?>

                        </td>
                        <td>
                            <a href="<?php echo e(route('admin.hs_sheet.show', $item->id)); ?>"><?php echo e($item->hs_code); ?></a>
                        </td>
                        <td>
                            <?php echo e($item->duty); ?>

                        </td>
                        <?php if(app()->getLocale() == 'en'): ?>
                            <td class="text-nowrap">
                                <?php if(\Illuminate\Support\Str::length($item->name_en) > 30): ?>
                                    <?php echo e(\Illuminate\Support\Str::limit($item->name_en, 30)); ?>

                                    <span class="text-primary showMore" data-text="<?php echo e($item->name_en); ?>"
                                        data-title = "<?php echo e(__('English Name')); ?>"><?php echo e(__('Show More')); ?></span>
                                <?php else: ?>
                                    <?php echo e($item->name_en); ?>

                                <?php endif; ?>
                            </td>
                        <?php else: ?>
                            <td class="text-nowrap">
                                <?php if(\Illuminate\Support\Str::length($item->name_ar) > 30): ?>
                                    <?php echo e(\Illuminate\Support\Str::limit($item->name_ar, 30)); ?>

                                    <span class="text-primary showMore" data-text="<?php echo e($item->name_ar); ?>"
                                        data-title = "<?php echo e(__('Arabic Name')); ?>"><?php echo e(__('Show More')); ?></span>
                                <?php else: ?>
                                    <?php echo e($item->name_ar); ?>

                                <?php endif; ?>
                            </td>
                        <?php endif; ?>
                        
                        
                        
                        

                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
    <br>
    <?php echo $__env->make('layouts.partials.pagination', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
    <div class="p-6">
        <div class="alert alert-warning text-center">
            <?php echo e(__('Sorry, No Data Found')); ?>

        </div>
    </div>
<?php endif; ?>

<div class="modal fade" id="basicModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel1"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="PageNo" value="<?php echo e($currentPage); ?>" />
<input type="hidden" id="OrderBy" value="<?php echo e($orderBy); ?>" />
<input type="hidden" id="Direction" value="<?php echo e($direction); ?>" />

<script>
    $(document).on("click", ".showMore", function() {
        var text = $(this).attr("data-text");
        var title = $(this).attr("data-title");
        //show modal
        $(".modal-title").text(title);
        $(".modal-body").text(text);
        $("#basicModal").modal("show")
    })
</script>
<?php /**PATH E:\clearo\resources\views/admin/hs_sheet/list.blade.php ENDPATH**/ ?>