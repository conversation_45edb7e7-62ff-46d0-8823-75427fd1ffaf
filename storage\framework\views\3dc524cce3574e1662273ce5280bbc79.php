<!doctype html>

<html lang="<?php echo e(app()->getLocale()); ?>" class="light-style layout-navbar-fixed layout-menu-fixed layout-compact"
    dir="<?php echo e(app()->getLocale() == 'en' ? 'ltr' : 'rtl'); ?>" data-theme="theme-default"
    data-assets-path="<?php echo e(asset('theme/assets')); ?>/" data-template="vertical-menu-template-starter" data-style="light">

<head>
    <meta charset="utf-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

    <title><?php echo e($title); ?></title>

    <meta name="description" content="" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon"
        href="<?php echo e(asset(@get_setting('favicon') && get_setting('favicon') != '' ? get_setting('favicon') : 'theme/assets/img/favicon.ico')); ?>" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/fonts/fontawesome.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/fonts/tabler-icons.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/fonts/flag-icons.css')); ?>" />

    <!-- Core CSS -->

    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/css/rtl/core.css')); ?>"
        class="template-customizer-core-css" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/css/rtl/theme-default.css')); ?>"
        class="template-customizer-theme-css" />

    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/css/demo.css')); ?>" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/node-waves/node-waves.css')); ?>" />

    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/typeahead-js/typeahead.css')); ?>" />
    <!-- Vendor -->
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/libs/@form-validation/form-validation.css')); ?>" />

    <!-- Page CSS -->
    <!-- Page -->
    <link rel="stylesheet" href="<?php echo e(asset('theme/assets/vendor/css/pages/page-auth.css')); ?>" />

    <!-- Helpers -->
    <script src="<?php echo e(asset('theme/assets/vendor/js/helpers.js')); ?>"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->

    <!--? Template customizer: To hide customizer set displayCustomizer value false in config.js.  -->
    <script src="<?php echo e(asset('theme/assets/vendor/js/template-customizer.js')); ?>"></script>

    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script src="<?php echo e(asset('theme/assets/js/config.js')); ?>"></script>

    <?php echo $__env->yieldPushContent('css'); ?>
</head>

<body>
    <!-- Content -->

    <div class="authentication-wrapper authentication-cover">
        <!-- Logo -->
        <a href="" class="app-brand auth-cover-brand">
            <span class="">
                <img src="<?php echo e(asset(@get_setting('favicon') && get_setting('favicon') != '' ? get_setting('favicon') : 'theme/assets/img/favicon.ico')); ?>"
                    alt="">
                
            </span>
            <span class="app-brand-text demo text-heading fw-bold"><?php echo e(get_site_name()); ?></span>
        </a>
        <!-- /Logo -->
        <div class="authentication-inner row m-0">
            <!-- /Left Text -->
            <div class="d-none d-lg-flex col-lg-8 p-0">
                <div class="auth-cover-bg auth-cover-bg-color d-flex justify-content-center align-items-center">
                    <img src="<?php echo e(asset('theme/assets/img/illustrations/auth-login-illustration-light.png')); ?>"
                        alt="auth-login-cover" class="my-5 auth-illustration"
                        data-app-light-img="illustrations/auth-login-illustration-light.png"
                        data-app-dark-img="illustrations/auth-login-illustration-dark.png" />

                    <img src="<?php echo e(asset('theme/assets/img/illustrations/bg-shape-image-light.png')); ?>"
                        alt="auth-login-cover" class="platform-bg"
                        data-app-light-img="illustrations/bg-shape-image-light.png"
                        data-app-dark-img="illustrations/bg-shape-image-dark.png" />
                </div>
            </div>
            <!-- /Left Text -->

            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>

    <!-- / Content -->

    <!-- Core JS -->
    <!-- build:js assets/vendor/js/core.js -->

    <script src="<?php echo e(asset('theme/assets/vendor/libs/jquery/jquery.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/popper/popper.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/js/bootstrap.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/node-waves/node-waves.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/hammer/hammer.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/i18n/i18n.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/typeahead-js/typeahead.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/js/menu.js')); ?>"></script>

    <!-- endbuild -->

    <!-- Vendors JS -->
    <script src="<?php echo e(asset('theme/assets/vendor/libs/@form-validation/popular.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/@form-validation/bootstrap5.js')); ?>"></script>
    <script src="<?php echo e(asset('theme/assets/vendor/libs/@form-validation/auto-focus.js')); ?>"></script>

    <!-- Main JS -->
    <script src="<?php echo e(asset('theme/assets/js/main.js')); ?>"></script>

    <!-- Page JS -->
    

    <?php echo $__env->yieldPushContent('js'); ?>
</body>

</html>
<?php /**PATH E:\clearo\resources\views/layouts/auth.blade.php ENDPATH**/ ?>