@extends('layouts.app')

@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        <div class="row g-4">
            @if (is_admin())
                @include('admin.dashboard.components.admin')
            @elseif (is_broker())
                @include('admin.dashboard.components.broker')
            @elseif (is_system_employee())
                @include('admin.dashboard.components.system_employee')
            @elseif (is_client() || is_sub_client())
                @include('admin.dashboard.components.client')
            @endif
        </div>
    </div>

    @include('admin.dashboard.components.search_shipping')
    @include('admin.dashboard.components.search_hs_code')
    @include('admin.dashboard.components.print_account_statement')
@endsection

@push('js')
    <script>
        function showSearchModal() {
            $(".error-info").addClass("d-none");
            $("#basicCrudModal").modal("show");
        }

        function showHsCodeModal() {
            $(".error-info").addClass("d-none");
            $("#searchHsCodeModal").modal("show");
        }

        function showAccountStatementModal() {
            $(".error-info").addClass("d-none");
            $("#printAccountStatementMondal").modal("show");
        }

        // Enhanced card interactions
        $(document).ready(function() {
            // Add click functionality to dashboard cards
            $('.dashcard').on('click', function() {
                const route = $(this).data('route');
                if (route) {
                    window.location.href = route;
                }
            });

            // Add hover effects
            $('.dashcard').hover(
                function() {
                    $(this).find('.card').addClass('shadow-lg');
                    $(this).css('transform', 'translateY(-5px)');
                },
                function() {
                    $(this).find('.card').removeClass('shadow-lg');
                    $(this).css('transform', 'translateY(0)');
                }
            );

            // Add loading animation for cards with data-route
            $('.dashcard[data-route]').on('click', function() {
                const card = $(this);
                const originalContent = card.html();

                // Show loading state
                card.find('.card-body').html(`
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 mb-0">{{ __('Loading...') }}</p>
                    </div>
                `);

                // Restore original content after a short delay if navigation fails
                setTimeout(() => {
                    card.html(originalContent);
                }, 3000);
            });
        });
    </script>
@endpush
