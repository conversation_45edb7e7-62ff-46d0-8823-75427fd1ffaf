<?php if($_Items != null && count($_Items) > 0): ?>
    <div class="card-datatable table-responsive">
        <table class="dt-advanced-search table">
            <thead class="table-light text-nowrap">
                <tr>
                    <th>#</th>
                    <th><?php echo e(__('Company Name')); ?>

                        <span class="arrows-container">
                            <i class="fas fa-caret-up orderbyArrow fadeIn animated" data-name="name" data-dir="asc"></i>
                            <i class="fas fa-caret-down orderbyArrow fadeIn animated" data-name="name" data-dir="desc"></i>
                        </span>
                    </th>
                    <th><?php echo e(__('Documents Count')); ?>


                    </th>
                    <th><?php echo e(__('View')); ?>

                    </th>
                </tr>
            </thead>
            <tbody>
                <?php
                    $index = $startFrom;
                ?>
                <?php $__currentLoopData = $_Items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php echo e(++$index); ?>

                        </td>
                        <td>
                            <a href="<?php echo e(route('admin.documents.typesPage', $item->id)); ?>">
                                <?php echo e($item->name); ?>

                            </a>
                        </td>
                        <td>
                            <?php echo e($item->documents_count); ?>

                        </td>

                        <td>
                            <div class="d-flex order-actions">
                                <?php if(auth()->user()->IsSuperAdmin == 1 || auth()->user()->can('View Documents')): ?>
                                    <a href="<?php echo e(route('admin.documents.typesPage', $item->id)); ?>" class="text-info"><i
                                            class='fas fa-eye'></i></a>
                                <?php endif; ?>
                            </div>
                        </td>

                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
    <br>
    <?php echo $__env->make('layouts.partials.pagination', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
    <div class="p-6">
        <div class="alert alert-warning text-center">
            <?php echo e(__('Sorry, No Data Found')); ?>

        </div>
    </div>
<?php endif; ?>

<input type="hidden" id="PageNo" value="<?php echo e($currentPage); ?>" />
<input type="hidden" id="OrderBy" value="<?php echo e($orderBy); ?>" />
<input type="hidden" id="Direction" value="<?php echo e($direction); ?>" />

<script></script>
<?php /**PATH E:\clearo\resources\views/admin/documents/document_types.blade.php ENDPATH**/ ?>