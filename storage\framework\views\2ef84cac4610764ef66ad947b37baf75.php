<?php $__env->startPush('css'); ?>
    <style>
        .table th,
        .table td {
            padding: 4px !important;
            font-size: 14px;
            /* Adjust font size if needed */
            line-height: 1.2;
            /* Reduce line spacing */
        }

        .table td {
            font-size: 13px;
        }

        .text-align-right {
            text-align: right;
        }

        .text-align-left {
            text-align: left;
        }

        h1 {
            color: #3366cc;
            text-align: center;
            margin-bottom: 20px;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 15px;
        }

        .logo-container img {
            width: 100px;
        }

        .date-info {
            text-align: <?php echo e(app()->getLocale() == 'ar' ? 'right' : 'left'); ?>;
            font-size: 10pt;
            color: #666;
            margin-bottom: 10px;
        }

        body {
            font-size: 10pt;
            color: #333;
            line-height: 1.5;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Make the main content area flexible */
        .invoice-print {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Push footer to bottom */
        .footer {
            margin-top: auto;
        }

        /* Ensure main content area takes available space */
        .main-content {
            flex: 1;
        }

        /* Additional styling for better layout */
        .invoice-content {
            flex-grow: 1;
        }

        /* Ensure each page has proper height */
        @media screen {
            html, body {
                height: 100%;
            }
        }

        /* For multi-page documents */
        .invoice-print:not(:last-child) {
            min-height: 100vh;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border-radius: 6px !important;
        }

        table th {
            background-color: #3366cc;
            color: #fff;
            font-weight: bold;
            text-align: <?php echo e(app()->getLocale() == 'ar' ? 'right' : 'left'); ?>;
            padding: 8px;
            border: 1px solid #dee2e6;
            font-size: 15px
        }

        table td {
            padding: 8px;
            border: 1px solid #dee2e6;
            text-align: <?php echo e(app()->getLocale() == 'ar' ? 'right' : 'left'); ?>;
            font-size: 15px
        }

        table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .footer {
            text-align: center;
            font-size: 9pt;
            color: #777;
            border-top: 1px solid #ccc;
            padding-top: 10px;
            margin-top: 20px;
        }

        .custom-table th,
        .custom-table td {
            text-align: center;
        }

        .company-info {
            font-size: 9pt;
            color: #555;
            line-height: 1.3;
            margin-top: 5px;
        }

        .company-info-arabic {
            text-align: right;
            direction: rtl;
        }

        .company-info-english {
            text-align: left;
            direction: ltr;
        }

        strong {
            font-size: 13px;
            color: #000
        }

        strong p {
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .invoice-footer {
            margin-top: 20px;
            padding: 10px 0;
        }

        .signature-row {
            display: flex;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 30px;
        }

        .acknowledgment-arabic {
            text-align: right;
            direction: rtl;
            font-size: 14px;
            font-weight: bold;
            vertical-align: middle;
        }

        .acknowledgment-english {
            text-align: left;
            direction: ltr;
            font-size: 14px;
            font-weight: bold;
            vertical-align: middle;
        }

        .signature-receiver {
            display: table-cell;
            width: 50%;
            text-align: right;
            direction: rtl;
            vertical-align: top;
            padding-right: 10px;
        }

        .signature-salesman {
            display: table-cell;
            width: 50%;
            text-align: left;
            direction: ltr;
            vertical-align: top;
            padding-left: 10px;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            width: 200px;
            margin-bottom: 5px;
        }

        .signature-label {
            font-size: 10pt;
            color: #666;
        }

        .company-logo {
            width: 60px;
            height: auto;
            margin-bottom: 10px;
        }

        .company-details ul {
            padding: 0;
            margin: 0;
            line-height: 1.6;
            font-size: 16px;
        }

        .company-details li {
            margin-bottom: 3px;
            color: #333;
        }

        .company-name-arabic {
            font-weight: bold;
            font-size: 11pt;
        }

        .company-name-english {
            font-weight: bold;
            font-size: 11pt;
            color: #555;
        }

        .account-info {
            color: #666;
            font-family: monospace;
        }

        .f-bold span {
            font-weight: bold;
        }

        /* Print optimizations - Force colors in PDF */
        @media print {

            /* Hide browser's automatic header and footer */
            @page {
                size: A4 portrait;
                margin: 10mm 8mm 10mm 8mm;
            }

            /* Hide browser default headers/footers */
            body {
                margin: 0 !important;
                padding: 0 !important;
                min-height: 100vh !important;
                display: flex !important;
                flex-direction: column !important;
            }

            /* Adjust content width for portrait printing */
            .invoice-print {
                max-width: 100% !important;
                width: 100% !important;
                margin: 0 !important;
                padding: 5px !important;
                box-sizing: border-box !important;
                flex: 1 !important;
                display: flex !important;
                flex-direction: column !important;
                min-height: 100vh !important;
            }

            /* Ensure footer stays at bottom during print */
            .footer {
                margin-top: auto !important;
                page-break-inside: avoid !important;
            }

            /* Ensure content fits in portrait mode */
            table {
                width: 100% !important;
                table-layout: fixed !important;
                word-wrap: break-word !important;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Force table header colors in PDF */
            thead {
                background-color: #3366cc !important;
                color: #ffffff !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            thead th {
                background-color: #3366cc !important;
                color: #ffffff !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Force background colors for all styled elements */
            [style*="background"] {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Specific fixes for header backgrounds */
            [style*="linear-gradient"] {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Force border colors */
            [style*="border"] {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Hide browser's automatic print headers and footers */
            html {
                -webkit-print-color-adjust: exact;
            }

            /* Remove any automatic page numbering or URLs */
            body::before,
            body::after {
                display: none !important;
            }

            /* Ensure no overflow on right side */
            * {
                box-sizing: border-box !important;
                max-width: 100% !important;
            }

            /* Specific adjustments for table cells to prevent overflow */
            td,
            th {
                word-break: break-word !important;
                overflow-wrap: break-word !important;
                max-width: none !important;
            }

            /* Adjust grid layouts for better fit */
            [style*="grid-template-columns"] {
                grid-template-columns: 1fr 1fr !important;
            }

            /* Ensure flex items don't overflow */
            [style*="display: flex"],
            [style*="display:flex"] {
                flex-wrap: wrap !important;
            }
        }

        /* Also apply for all media to ensure compatibility */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        thead {
            background-color: #3366cc !important;
            color: #ffffff !important;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        thead th {
            background-color: #3366cc !important;
            color: #ffffff !important;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        // Hide browser's automatic print headers and footers
        window.addEventListener('beforeprint', function() {
            // Set page margins to hide default headers/footers
            var style = document.createElement('style');
            style.innerHTML = `
            @page {
                size: A4 portrait;
                margin: 8mm 6mm 8mm 6mm;
            }
            @media print {
                body {
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }
                /* Hide any automatic browser elements */
                body::before, body::after {
                    display: none !important;
                }
            }
        `;
            document.head.appendChild(style);
        });

        // Adjust content for better printing
        window.addEventListener('load', function() {
            // Ensure content fits properly
            document.body.style.margin = '0';
            document.body.style.padding = '0';
        });
    </script>
<?php $__env->stopPush(); ?>



<?php $__env->startSection('content'); ?>

    <?php $__currentLoopData = $expenseChunks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chunk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="invoice-print px-8">
            <div class="main-content">
                <div class="header"
                style=" background-color: #f3f4f6; padding: 2px 5px; margin-bottom: 10px; border-radius: 5px;">
                <table style="width: 100%; border: none; margin: 0; padding: 0; border-collapse: collapse;">
                    <tr>
                        <td style="width: 40%; border: none; vertical-align: top; padding: 5px;">
                            <div class="company-info company-info-arabic"
                                style="font-family: 'Almarai', sans-serif !important;">
                                <strong>
                                    <p>كليرو لخدمات الشحن والتخليص</p>
                                    <p>العنوان: الكويت - سوق الصفاة </p>
                                    <p>الدور الثاني- مكتب رقم 5</p>
                                    <p>الهاتف: <?php echo e(get_setting('phone')); ?></p>
                                    <p> الفاكس: <?php echo e(get_setting('company_fax') ?? '22442977'); ?></p>
                                </strong>
                            </div>
                        </td>

                        <td style="width: 20%; text-align: center; border: none; vertical-align: middle; padding: 0;">
                            <?php if($logo): ?>
                                <div class="logo-container" style="margin: 10px 0 0 0;">
                                    <img src="<?php echo e(asset(isset($logo) && $logo != '' ? $logo : 'theme/assets/img/default-logo.png')); ?>"
                                        alt="Logo" width="100">
                                </div>
                            <?php endif; ?>
                        </td>

                        <td
                            style="width: 40%; border: none; vertical-align: top; padding: 5px; direction: ltr; text-align: left;">
                            <div class="company-info company-info-english">
                                <strong>
                                    <p>Clearo for Shipping and Clearance Services</p>
                                    <p>Address: Kuwait - Souk Al-Safat</p>
                                    <p>2nd Floor - Office No.5</p>
                                    <p>Phone: <?php echo e(get_setting('phone')); ?></p>
                                    <p>Fax: <?php echo e(get_setting('company_fax') ?? '22442977'); ?></p>
                                </strong>
                            </div>
                        </td>
                    </tr>
                    <tr>
                    </tr>
                </table>
            </div>
            <div style="width: fit-content; margin: 0 auto 5px;">
                <h1
                    style="margin-top: 0; margin-bottom: -2px; font-weight: semibold; color: #000; font-size: 22px; line-height: 1.2;color: #3366cc;">
                    <?php echo e($title ?? __('INVOICE')); ?></h1>
                <hr style="margin: 0; height: 3px; background-color: #3366cc;" />
            </div>
            <div>

                <div class="row f-bold mb-1"
                    style="<?php echo e(app()->getLocale() == 'ar' ? 'direction: rtl;' : 'direction: ltr; text-align: left;'); ?>">
                    <div class="col-sm-5">
                        <p class="mb-0"><span><?php echo e(__('INVOICE')); ?> : </span>#<?php echo e($invoice->invoice_number); ?></p>
                        <p class="mb-0"><span><?php echo e(__('Company Name')); ?> :
                            </span><?php echo e(@$invoice->customShipping?->company->name); ?></p>
                        <p class="mb-0"><span><?php echo e(__('Policy Number')); ?> :
                            </span><?php echo e($invoice->customShipping?->policy_number); ?></p>
                    </div>
                    <div class="col-sm-4">

                        <p class="mb-0"><span><?php echo e(__('Shipping Code')); ?> : </span><?php echo e($invoice->customShipping?->code); ?>

                        </p>
                        <p class="mb-0"><span><?php echo e(__('Packages Count')); ?>:
                            </span><?php echo e($invoice->customShipping?->packages_count); ?></p>
                        <p class="mb-0"><span><?php echo e(__('Total Weight')); ?>:
                            </span><?php echo e($invoice->customShipping?->total_weight); ?>

                        </p>
                    </div>
                    <div class="col-sm-3">
                        <p class="mb-0"><span><?php echo e(__('Client')); ?> :</span><?php echo e($invoice->user->name); ?></p>
                        <p class="mb-0">
                            <span><?php echo e(__('Invoice Date')); ?>:</span><?php echo e($invoice->created_at->format('F d, Y')); ?>

                        </p>
                        <p class="mb-0"><span><?php echo e(__('Current Date')); ?>:</span><?php echo e(now()->format('F d, Y')); ?></p>
                    </div>
                </div>
                <!-- <hr class="mb-2" /> -->
                <div class="table-responsive border border-bottom-0 rounded"
                    style="<?php echo e(app()->getLocale() == 'ar' ? 'direction: rtl; text-align: right;' : 'direction: ltr; text-align: left;'); ?>">
                    <table class="table m-0">
                        <thead style="background-color: #3366cc; color: #FFF !important;">

                            <th style="color: #FFF !important;" colspan="1">#</th>
                            <th style="color: #FFF !important;" colspan="4"><?php echo e(__('Expense Name')); ?></th>
                            <th style="color: #FFF !important;" colspan="2"><?php echo e(__('Amount')); ?></th>
                            <th style="color: #FFF !important;" colspan="5"><?php echo e(__('Notes')); ?></th>

                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $chunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td colspan="1"><?php echo e($index + 1); ?></td>
                                    <td colspan="4">
                                        <?php if(app()->getLocale() == 'ar'): ?>
                                            <?php echo e($item->shippingExpenseType->name_ar); ?>

                                        <?php else: ?>
                                            <?php echo e($item->shippingExpenseType->name_en); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td colspan="2"><?php echo e(number_format($item->amount, 2)); ?></td>
                                    <td colspan="5"><?php echo e($item->notes); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                        <tfoot class="table-light table-lighter"
                            style="background-color: #3366cc !important; color: #FFF !important; font-weight: bold">
                            <tr>
                                <td style="background-color: #3366cc !important; color: #FFF !important;" colspan="5">
                                    <?php echo e(__('Total Amount')); ?></td>
                                <td style="background-color: #3366cc !important; color: #FFF !important;" colspan="2">
                                    <?php echo e(number_format($invoice->total_amount, 2)); ?></td>
                                <td style="background-color: #3366cc !important; color: #FFF !important;" colspan="5">
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                    <table class="table m-0 d-none">
                        <thead style="background-color: #3366cc; color: #FFF !important;">

                            <th style="color: #FFF !important;" colspan="1">#</th>
                            <th style="color: #FFF !important;" colspan="4"><?php echo e(__('Expense Name')); ?></th>
                            <th style="color: #FFF !important;" colspan="2"><?php echo e(__('Amount')); ?></th>
                            <th style="color: #FFF !important;" colspan="5"><?php echo e(__('Notes')); ?></th>

                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $chunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td colspan="1"><?php echo e($index + 1); ?></td>
                                    <td colspan="4">
                                        <?php if(app()->getLocale() == 'ar'): ?>
                                            <?php echo e($item->shippingExpenseType->name_ar); ?>

                                        <?php else: ?>
                                            <?php echo e($item->shippingExpenseType->name_en); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td colspan="2"><?php echo e(number_format($item->amount, 2)); ?></td>
                                    <td colspan="5"><?php echo e($item->notes); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                        <tfoot class="table-light table-lighter"
                            style="background-color: #3366cc !important; color: #FFF !important; font-weight: bold">
                            <tr>
                                <td style="background-color: #3366cc !important; color: #FFF !important;" colspan="5">
                                    <?php echo e(__('Total Amount')); ?></td>
                                <td style="background-color: #3366cc !important; color: #FFF !important;" colspan="2">
                                    <?php echo e(number_format($invoice->total_amount, 2)); ?></td>
                                <td style="background-color: #3366cc !important; color: #FFF !important;" colspan="5">
                                </td>
                            </tr>
                        </tfoot>
                    </table>

                </div>

                <!-- Acknowledgment Row -->
                <div class="acknowledgment-row my-3"
                    style="<?php echo e(app()->getLocale() == 'ar' ? 'direction: rtl; text-align: right;' : 'direction: ltr; text-align: left;'); ?>">
                    <span style="font-weight: bold;">
                        <?php echo e(__('I received the goods in good condition and undertake to pay upon request')); ?>

                    </span>
                </div>



                <!-- Signature Row -->
                <div class="signature-row">
                    <div class="signature-receiver">
                        <div class="signature-line"></div>
                        <div class="signature-label">توقيع المستلم - Receiver's Sign</div>
                    </div>
                    <div class="signature-salesman">
                        <div class="signature-line"></div>
                        <div class="signature-label">توقيع البائع - Salesman Sign</div>
                    </div>
                </div>
            </div>
            <!-- End main-content -->

            <!-- Company Details -->
                <?php if(is_admin() || is_client()): ?>
                    <?php if(app()->getLocale() == 'en'): ?>
                        <div class="footer d-flex justify-content-between align-items-center"
                            style="direction: ltr; text-align: left;padding: 0 20px;">
                            <div class="company-details">
                                <ul>
                                    <!-- <li><?php echo e(get_setting('company_name')); ?></li> -->
                                    <li><strong>Customer Name:</strong> Ibrahim Elbadri Ibrahim Megahed</li>
                                    <li> <strong>ACCOUNT NO:</strong> ************</li>
                                    <li><strong>IBAN NO:</strong> ******************************</li>
                                </ul>
                            </div>
                            <div style="font-size: 16px">
                                <!-- <p class="mb-1"><?php echo e(__('Please make all checks payable to Mr. Ibrahim Elbadri')); ?></p>
                                                        <p class="mb-1">Ibrahim Megahed,Managing Director of CLEARO Company</p>
                                                        <p class="mb-1"><?php echo e(__('Thank you for your business!')); ?></p> -->
                                <?php if($logo): ?>
                                    <img src="<?php echo e(asset('theme/assets/img/kfh-logo.png')); ?>" alt="Logo"
                                        style="width: 120px; display: block; margin: 0 100px;">
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="footer d-flex justify-content-between align-items-center"
                            style="<?php echo e(app()->getLocale() == 'ar' ? 'direction: rtl;' : 'direction: ltr;'); ?> padding: 0 20px;">
                            <div class="company-details">
                                <ul>
                                    <!-- <li><?php echo e(get_setting('company_name')); ?></li> -->
                                    <li><strong>اسم العميل:</strong> إبراهيم البدري إبراهيم مجاهد</li>
                                    <li><strong>رقم الحساب:</strong> 061320007982</li>
                                    <li><strong>رقم الآيبان:</strong> KW05KFHO0000000000061320007982</li>
                                </ul>
                            </div>
                            <div class="text-align-right"style="font-size: 16px">
                                <!-- <p class="mb-1">يرجى تحرير جميع الشيكات باسم السيد/ إبراهيم البدري إبراهيم مجاهد،</p>
                                                        <p class="mb-1">المدير التنفيذي لشركة CLEARO.</p>
                                                        <p class="mb-1">شكرًا لتعاملكم معنا!</p> -->
                                <?php if($logo): ?>
                                    <img src="<?php echo e(asset('theme/assets/img/kfh-logo.png')); ?>" alt="Logo"
                                        style="width: 120px; display: block; margin: 0 100px;">
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                <!-- </div> -->
            </div>
        </div>
        <?php if(!$loop->last): ?>
            <div style="page-break-after: always;"></div>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.print', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\clearo\resources\views/admin/shipping_invoices/invoice_print.blade.php ENDPATH**/ ?>