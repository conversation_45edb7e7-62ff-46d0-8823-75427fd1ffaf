@extends('layouts.app')

@push('css')
    <link rel="stylesheet" href="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endpush
@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        @include('components.page-title', ['title' => $title])
        @if (is_admin())
            <div class="card">
                <div class="row py-6 px-6">
                    <div class="col-md-4">
                        <div class="row">
                            <label for="html5-search-input" class="col-md-2 col-form-label"
                                style="text-wrap:nowrap;">{{ __('Search') }}</label>
                            <div class="col-md-10">
                                <input class="form-control" type="search" id="Search"
                                    placeholder="{{ __('Search...') }}" id="html5-search-input" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4"></div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center justify-content-end">
                            <div class="">{{ __('show') }}</div>
                            <div class="mx-2">
                                <select name="RowsPerPage" id="RowsPerPage" class="form-control">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <div class="">{{ __('entities') }}</div>
                        </div>
                    </div>
                </div>
                <div id="ListBodyDiv"></div>
            </div>
        @else
            <div class="row">
                <div class="col-md-4">
                    <div class="col-lg-12 order-3 order-xl-0">
                        <div class="card">
                            <div class="row">
                                <div class="">
                                    <div class="card-body text-nowrap">
                                        <h5 class="card-title mb-0">{{ __('Client Debit') }}</h5>
                                        @if ($client_debit->amount > 0)
                                            <h4 class="text-primary mb-1">{{ $client_debit->amount }}</h4>
                                        @else
                                            <h4 class="text-danger mb-1">{{ $client_debit->amount }}</h4>
                                        @endif
                                    </div>
                                    <div class="card-body text-nowrap">
                                        <h5 class="card-title mb-0">{{ __('Wallet Balance') }}</h5>
                                        @if ($wallet_balance > 0)
                                            <h4 class="text-primary mb-1">{{ $wallet_balance }}</h4>
                                        @else
                                            <h4 class="text-danger mb-1">{{ $wallet_balance }}</h4>
                                        @endif
                                    </div>
                                </div>
                                <div class="py-4">
                                    @if (auth()->user()->IsSuperAdmin == 1 ||
                                            auth()->user()->can('Update Client Debit') ||
                                            auth()->user()->type == UserType::Client)
                                        <a href="javascript:;"
                                            onclick="confirmFullPay('{{ $client_debit->id }}','{{ $client_debit->amount }}')"
                                            class="ms-3 btn btn-sm btn-danger waves-effect waves-light text-nowrap mx-2">
                                            {{ __('Full Pay') }}
                                        </a>
                                    @endif
                                    @if (auth()->user()->IsSuperAdmin == 1 ||
                                            auth()->user()->can('Update Client Debit') ||
                                            auth()->user()->type == UserType::Client)
                                        <a href="javascript:;"
                                            onclick="showPartialPayModal('{{ $client_debit->id }}','{{ $client_debit->amount }}')"
                                            class="ms-3 btn btn-sm btn-secondary waves-effect waves-light text-nowrap mx-2">
                                            {{ __('Partial Pay') }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

    </div>

    @include('admin.client_debits.partials.update_balance')
    @include('admin.client_debits.partials.balance_actions')
@endsection

@push('js')
    <script src="{{ asset('theme/assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script>
        function LoadData(pageNo) {
            var search = $("#Search").val();
            var orderBy = $("#OrderBy").val();
            var direction = $("#Direction").val();
            var rowsPerPage = $("#RowsPerPage").val();
            var token = $("#token").val();
            $.ajax({
                type: 'POST',
                url: "{{ route('admin.client_debits.list') }}",
                data: {
                    PageNo: pageNo,
                    Search: search,
                    OrderBy: orderBy,
                    Direction: direction,
                    RowsPerPage: rowsPerPage,
                    _token: token
                },
                cache: false,
                dataType: "html",
                success: function(res) {
                    $("#ListBodyDiv").html(res);
                    var el = $(`i[data-name='${orderBy}'][data-dir='${direction}']`);
                    el.addClass("arrow-selected");
                },
                error: function(err) {
                    console.log(err)
                }
            });
        }


        $(document).ready(function() {
            LoadData(1);
        });

        $(".searchBtn").on("click", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $("#RowsPerPage").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function clearSearch() {
            $("#Search").val("");
            $("#Role").val("null");

            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        }

        $(document).on("keypress", function(e) {
            if (e.keyCode == 13) {
                var pageNo = $("#PageNo").val();
                LoadData(pageNo);
            }
        });
        $("#Search").on("change", function() {
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });
        $(document).on("click", ".orderbyArrow", function() {

            var name = $(this).attr("data-name");
            var direction = $(this).attr("data-dir");

            $("#Direction").val(direction);
            $("#OrderBy").val(name);
            var pageNo = $("#PageNo").val();
            LoadData(pageNo);
        });

        function deleteUser(id) {
            Swal.fire({
                title: "{{ __('Are you Sure?') }}",
                text: "{{ __('data will be deleted permenantly') }}",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "{{ __('Yes') }}",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    var token = $("#token").val();
                    $.post("{{ url('admin/client_debits/') }}" + "/" + id, {
                            _token: token,
                            _method: "DELETE"
                        },
                        function(data, status) {
                            if (status == "success") {
                                Swal.fire({
                                    icon: 'success',
                                    title: "{{ __('Deleted Successfully') }}",
                                    text: "{{ __('data has been deleted successfully') }}",
                                    customClass: {
                                        confirmButton: 'btn btn-success waves-effect waves-light'
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: "{{ __('error') }}",
                                    text: "{{ __('something went wrong please try later') }}",
                                })
                            }
                            var pageNo = $("#PageNo").val();
                            LoadData(pageNo);
                        });
                }
            });

        }
        (function() {
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            const popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        })();



        function __confirmFullPay(item_id) {
            Swal.fire({
                title: "{{ __('Are you Sure?') }}",
                text: "{{ __('user balance will be set to zero') }}",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#28bb4b",
                confirmButtonText: "{{ __('Yes') }}",
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then(function(result) {
                if (result.value) {
                    $("#action_type").val("{{ WorkerDueAction::FullPay }}")
                    $("#_method").val("PUT")
                    $("#ClientDebitForm").attr("action", "{{ url('/admin/client_debits') }}" + `/${item_id}`)
                    $("#ClientDebitForm").submit();
                    var token = $("#token").val();
                }
            });
        }
    </script>
@endpush
